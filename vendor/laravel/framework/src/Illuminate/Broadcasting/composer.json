{"name": "illuminate/broadcasting", "description": "The Illuminate Broadcasting package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.1.3", "psr/log": "~1.0", "illuminate/bus": "5.6.*", "illuminate/contracts": "5.6.*", "illuminate/queue": "5.6.*", "illuminate/support": "5.6.*"}, "autoload": {"psr-4": {"Illuminate\\Broadcasting\\": ""}}, "extra": {"branch-alias": {"dev-master": "5.6-dev"}}, "suggest": {"pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (~3.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}