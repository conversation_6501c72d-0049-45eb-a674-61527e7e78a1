{"name": "illuminate/cache", "description": "The Illuminate Cache package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.1.3", "illuminate/contracts": "5.6.*", "illuminate/support": "5.6.*"}, "autoload": {"psr-4": {"Illuminate\\Cache\\": ""}}, "extra": {"branch-alias": {"dev-master": "5.6-dev"}}, "suggest": {"illuminate/database": "Required to use the database cache driver (5.6.*).", "illuminate/filesystem": "Required to use the file cache driver (5.6.*).", "illuminate/redis": "Required to use the redis cache driver (5.6.*)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}