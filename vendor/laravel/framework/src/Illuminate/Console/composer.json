{"name": "illuminate/console", "description": "The Illuminate Console package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.1.3", "illuminate/contracts": "5.6.*", "illuminate/support": "5.6.*", "symfony/console": "~4.0"}, "autoload": {"psr-4": {"Illuminate\\Console\\": ""}}, "extra": {"branch-alias": {"dev-master": "5.6-dev"}}, "suggest": {"dragonmantank/cron-expression": "Required to use scheduling component (~2.0).", "guzzlehttp/guzzle": "Required to use the ping methods on schedules (~6.0).", "symfony/process": "Required to use scheduling component (~4.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}