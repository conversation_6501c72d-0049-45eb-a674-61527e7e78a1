{"name": "illuminate/auth", "description": "The Illuminate Auth package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.1.3", "illuminate/contracts": "5.6.*", "illuminate/http": "5.6.*", "illuminate/queue": "5.6.*", "illuminate/support": "5.6.*"}, "autoload": {"psr-4": {"Illuminate\\Auth\\": ""}}, "extra": {"branch-alias": {"dev-master": "5.6-dev"}}, "suggest": {"illuminate/console": "Required to use the auth:clear-resets command (5.6.*).", "illuminate/queue": "Required to fire login / logout events (5.6.*).", "illuminate/session": "Required to use the session based guard (5.6.*)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}