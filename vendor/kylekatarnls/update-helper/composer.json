{"name": "kylekatarnls/update-helper", "description": "Update helper", "type": "composer-plugin", "license": "MIT", "minimum-stability": "dev", "prefer-stable": true, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.3.0", "composer-plugin-api": "^1.1.0 || ^2.0.0"}, "require-dev": {"composer/composer": "2.0.x-dev || ^2.0.0-dev", "phpunit/phpunit": ">=4.8.35 <6.0", "codeclimate/php-test-reporter": "dev-master"}, "autoload": {"psr-0": {"UpdateHelper\\": "src/"}}, "autoload-dev": {"psr-4": {"UpdateHelper\\Tests\\": "tests/UpdateHelper/"}}, "scripts": {"post-install-cmd": ["UpdateHelper\\UpdateHelper::check"], "post-update-cmd": ["UpdateHelper\\UpdateHelper::check"], "post-package-install": ["UpdateHelper\\UpdateHelper::check"], "post-package-update": ["UpdateHelper\\UpdateHelper::check"]}, "extra": {"class": "UpdateHelper\\ComposerPlugin"}}