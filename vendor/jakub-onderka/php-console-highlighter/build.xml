<?xml version="1.0" encoding="UTF-8"?>
<project default="build">
    <!-- Set executables according to OS -->
    <condition property="phpunit" value="${basedir}/vendor/bin/phpunit.bat" else="${basedir}/vendor/bin/phpunit">
        <os family="windows" />
    </condition>

    <condition property="phpcs" value="${basedir}/vendor/bin/phpcs.bat" else="${basedir}/vendor/bin/phpcs">
        <os family="windows" />
    </condition>

    <condition property="parallel-lint" value="${basedir}/vendor/bin/parallel-lint.bat" else="${basedir}/vendor/bin/parallel-lint">
        <os family="windows" />
    </condition>

    <condition property="var-dump-check" value="${basedir}/vendor/bin/var-dump-check.bat" else="${basedir}/vendor/bin/var-dump-check">
        <os family="windows"/>
    </condition>

    <!-- Use colors in output can be disabled when calling ant with -Duse-colors=false -->
    <property name="use-colors" value="true" />

    <condition property="colors-arg.color" value="--colors" else="">
        <equals arg1="${use-colors}" arg2="true" />
    </condition>

    <condition property="colors-arg.no-colors" value="" else="--no-colors">
        <equals arg1="${use-colors}" arg2="true" />
    </condition>

    <!-- Targets -->
    <target name="prepare" description="Create build directory">
        <mkdir dir="${basedir}/build/logs" />
    </target>

    <target name="phplint" description="Check syntax errors in PHP files">
        <exec executable="${parallel-lint}" failonerror="true">
            <arg line='--exclude ${basedir}/vendor/' />
            <arg line='${colors-arg.no-colors}' />
            <arg line='${basedir}' />
        </exec>
    </target>

    <target name="var-dump-check" description="Check PHP files for forgotten variable dumps">
        <exec executable="${var-dump-check}" failonerror="true">
            <arg line='--exclude ${basedir}/vendor/' />
            <arg line='${colors-arg.no-colors}' />
            <arg line='${basedir}' />
        </exec>
    </target>

    <target name="phpcs" depends="prepare" description="Check PHP code style">
        <delete file="${basedir}/build/logs/checkstyle.xml" quiet="true" />

        <exec executable="${phpcs}">
            <arg line='--extensions=php' />
            <arg line='--standard="${basedir}/vendor/jakub-onderka/php-code-style/ruleset.xml"' />
            <arg line='--report-checkstyle="${basedir}/build/logs/checkstyle.xml"' />
            <arg line='--report-full' />
            <arg line='"${basedir}/src"' />
        </exec>
    </target>

    <target name="phpunit" depends="prepare" description="PHP unit">
        <delete file="${basedir}/build/logs/phpunit.xml" quiet="true" />

        <exec executable="${phpunit}">
            <arg line='--configuration ${basedir}/phpunit.xml' />
            <arg line='-d memory_limit=256M' />
            <arg line='--log-junit "${basedir}/build/logs/phpunit.xml"' />
            <arg line='${colors-arg.color}' />
        </exec>
    </target>

    <target name="phpunit-coverage" depends="prepare" description="PHP unit with code coverage">
        <delete file="${basedir}/build/logs/phpunit.xml" quiet="true" />
        <delete file="${basedir}/build/logs/clover.xml" quiet="true" />
        <delete dir="${basedir}/build/coverage" quiet="true" />
        <mkdir dir="${basedir}/build/coverage" />

        <exec executable="${phpunit}" failonerror="true">
            <arg line='--configuration ${basedir}/phpunit.xml' />
            <arg line='-d memory_limit=256M' />
            <arg line='--log-junit "${basedir}/build/logs/phpunit.xml"' />
            <arg line='--coverage-clover "${basedir}/build/logs/clover.xml"' />
            <arg line='--coverage-html "${basedir}/build/coverage/"' />
            <arg line='${colors-arg.color}' />
        </exec>
    </target>

    <target name="build" depends="phplint,var-dump-check,phpcs,phpunit" />

</project>