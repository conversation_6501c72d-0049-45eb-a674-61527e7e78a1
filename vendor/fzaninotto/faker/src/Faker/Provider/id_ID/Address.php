<?php

namespace Faker\Provider\id_ID;

class Address extends \Faker\Provider\Address
{
    /**
     * @link http://bandung.go.id/images/download/daftarruasjalan.htm
     **/
    protected static $street = array(
        "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON><PERSON>", "<PERSON>chmad", "<PERSON>chmad <PERSON>i", "Acordion", "Adisucipto",
        "Adisumarmo", "Agus <PERSON>im", "<PERSON>", "Antapani Lama", "Arifin",
        "Asia Afrika", "Astana Anyar", "B.Agam 1", "B.Agam Dlm", "BKR",
        "Baabur <PERSON>", "Baan", "Babadak", "<PERSON>dan", "Baba<PERSON>",
        "Babakan", "Bacang", "Badak", "Bagas Pati", "Bagis Utama",
        "Bagonwoto ", "Bah Jaya", "Baha", "Bahagia", "Bahagia ",
        "<PERSON><PERSON><PERSON>", "Baik", "<PERSON>ng", "Baja", "Baja Raya",
        "Bak Air", "Bak Mandi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Bakau Griya Utama",
        "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Bakit ", "Bakti", "Baladewa",
        "Bambon", "Bambu", "Banal", "Banceng Pondok", "Banda",
        "Bank Dagang Negara", "Bappenas", "Bara", "Bara Tambar", "Baranang",
        "Baranang Siang", "Baranang Siang Indah", "Baranangsiang", "Barasak", "Barat",
        "Basket", "Basmol Raya", "Basoka", "Basoka Raya", "Bass",
        "Basudewo", "Basuki", "Basuki Rahmat ", "Bata Putih", "Batako",
        "Baung", "Bawal", "Baya Kali Bungur", "Bayam", "Bayan",
        "Bazuka Raya", "Bhayangkara", "Camar", "Casablanca", "Cemara",
        "Cihampelas", "Cikapayang", "Cikutra Barat", "Cikutra Timur", "Ciumbuleuit",
        "Ciwastra", "Cokroaminoto", "Cut Nyak Dien", "Daan", "Dago",
        "Dahlia", "Dewi Sartika", "Dipatiukur", "Dipenogoro", "Diponegoro",
        "Dr. Junjunan", "Eka", "Ekonomi", "Elang", "Fajar",
        "Flora", "Flores", "Gading", "Gajah", "Gajah Mada",
        "Gambang", "Gardujati", "Gatot Subroto", "Gedebage Selatan", "Gegerkalong Hilir",
        "Gotong Royong", "Gremet", "HOS. Cjokroaminoto (Pasirkaliki)", "Haji", "Halim",
        "Hang", "Hasanuddin", "Honggowongso", "Ikan", "Imam",
        "Imam Bonjol", "Industri", "Ir. H. Juanda", "Jagakarsa", "Jakarta",
        "Jaksa", "Jambu", "Jamika", "Jayawijaya", "Jend. A. Yani",
        "Jend. Sudirman", "Juanda", "K.H. Maskur", "K.H. Wahid Hasyim (Kopo)", "Kali",
        "Kalimalang", "Kalimantan", "Karel S. Tubun", "Kartini", "Katamso",
        "Kebangkitan Nasional", "Kebonjati", "Ketandan", "Ki Hajar Dewantara",
        "Kiaracondong", "Krakatau", "Kusmanto", "Kyai Gede", "Kyai Mojo", "Labu",
        "Lada", "Laksamana", "Laswi", "Lembong", "Lumban Tobing",
        "M.T. Haryono", "Madiun", "Madrasah", "Mahakam", "Merdeka",
        "Moch. Ramdan", "Moch. Toha", "Moch. Yamin", "Monginsidi", "Mulyadi",
        "Muwardi", "Nakula", "Nanas", "Nangka", "Orang",
        "Otista", "Otto", "PHH. Mustofa", "Pacuan Kuda", "Padang",
        "Padma", "Pahlawan", "Panjaitan", "Pasir Koja", "Pasirkoja",
        "Pasteur", "Pattimura", "Pelajar Pejuang 45", "Perintis Kemerdekaan", "Peta",
        "Qrisdoren", "R.E. Martadinata", "R.M. Said", "Raden", "Raden Saleh",
        "Radio", "Rajawali", "Rajawali Barat", "Rajawali Timur", "Rajiman",
        "Raya Setiabudhi", "Raya Ujungberung", "Reksoninten", "Ronggowarsito",
        "Rumah Sakit", "S. Parman", "Sadang Serang", "Salak", "Salam", "Salatiga",
        "Sam Ratulangi", "Samanhudi", "Sampangan", "Sentot Alibasa", "Setia Budi",
        "Setiabudhi", "Siliwangi", "Soekarno Hatta", "Sudiarto", "Sudirman",
        "Sugiono", "Sugiyopranoto", "Suharso", "Sukabumi", "Sukajadi",
        "Sumpah Pemuda", "Sunaryo", "Suniaraja", "Supomo", "Supono",
        "Suprapto", "Surapati", "Suryo", "Suryo Pranoto", "Sutami",
        "Sutan Syahrir", "Sutarjo", "Sutarto", "Sutoyo", "Taman",
        "Tambak", "Tambun", "Tangkuban Perahu", "Tentara Pelajar", "Ters. Buah Batu",
        "Ters. Jakarta", "Ters. Kiaracondong", "Ters. Pasir Koja", "Teuku Umar",
        "Thamrin", "Tubagus Ismail", "Ujung", "Uluwatu", "Umalas", "Untung Suropati",
        "Urip Sumoharjo", "Veteran", "Villa", "W.R. Supratman", "Wahid",
        "Wahid Hasyim", "Wahidin", "Wahidin Sudirohusodo", "Warga", "Wora Wari",
        "Yap Tjwan Bing", "Yoga", "Yogyakarta", "Yohanes", "Yos",
        "Yos Sudarso", "Yosodipuro", "Zamrud",
    );

    protected static $streetPrefix = array(
        "Ds.", "Dk.", "Gg.", "Jln.", "Jr.", "Kpg.", "Ki.", "Psr."
    );

    /**
     * @link http://kodepos.nomor.net/_kodepos.php?_i=provinsi-kodepos
     */
    protected static $state = array(
        'Aceh', 'Sumatera Utara', 'Sumatera Barat', 'Jambi', 'Bangka Belitung', 'Riau',
        'Kepulauan Riau', 'Bengkulu', 'Sumatera Selatan', 'Lampung', 'Banten',
        'DKI Jakarta', 'Jawa Barat', 'Jawa Tengah', 'Jawa Timur', 'Nusa Tenggara Timur',
        'DI Yogyakarta', 'Bali', 'Nusa Tenggara Barat', 'Kalimantan Barat',
        'Kalimantan Tengah', 'Kalimantan Selatan', 'Kalimantan Timur',
        'Kalimantan Utara', 'Sulawesi Selatan', 'Sulawesi Utara', 'Gorontalo',
        'Sulawesi Tengah', 'Sulawesi Barat', 'Sulawesi Tenggara', 'Maluku',
        'Maluku Utara', 'Papua Barat', 'Papua'
    );

    /**
     * Abbreviated State Names.
     * Currently this is all just a guesswork, but should be highly accurate
     * @var array
     */
    protected static $stateAbbr = array(
       'Aceh', 'SumUt', 'SumBar', 'Jambi', 'BaBel', 'Riau', 'KepR', 'Bengkulu', 'SumSel',
       'Lampung', 'Banten', 'DKI', 'JaBar', 'JaTeng', 'JaTim', 'NTT', 'DIY',
       'Bali', 'NTB', 'KalBar', 'KalTeng', 'KalSel', 'KalTim', 'KalUt', 'SulSel',
       'SulUt', 'Gorontalo', 'SulTeng', 'SulBar', 'SulTra', 'Maluku', 'MalUt',
       'PapBar', 'Papua'
    );

    /**
    * @link http://id.wikipedia.org/wiki/Daftar_negara-negara_di_dunia
    **/
    protected static $country = array(
        'Afganistan', 'Afrika Selatan', 'Albania', 'Aljazair',
        'Amerika Serikat', 'Andorra', 'Angola', 'Anguilla',

        // http://id.wikipedia.org/wiki/Antartika
        'Antarktika',

        'Antigua and Barbuda',

        // http://id.wikipedia.org/wiki/Antillen_Belanda
        'Antillen Belanda',

        'Arab Saudi', 'Argentina', 'Armenia', 'Aruba', 'Australia', 'Austria',
        'Azerbaijan', 'Bahama', 'Bahrain', 'Bangladesh', 'Barbados', 'Belanda',
        'Belarus', 'Belgia', 'Belize', 'Benin', 'Bermuda', 'Bhutan', 'Bolivia',

        // http://id.wikipedia.org/wiki/Bosnia_dan_Herzegovina
        'Bosnia dan Herzegovina',

        'Botswana', 'Brasil', 'Brunei Darussalam', 'Bulgaria', 'Burkina Faso',
        'Burundi', 'Chili', 'Cina', 'Cocos (Keeling)', 'Denmark', 'Djibouti',

        // http://id.wikipedia.org/wiki/Dominica
        'Dominika',

        'Ekuador', 'El Salvador', 'Eritrea', 'Estonia', 'Ethiopia',
        'Federasi Rusia', 'Fiji', 'Filipina', 'Finlandia', 'Gabon', 'Gambia',
        'Georgia',

        // http://id.wikipedia.org/wiki/South_Georgia_and_the_South_Sandwich_Islands
        'Georgia Selatan dan Kepulauan Sandwich Selatan',

        'Ghana', 'Gibraltar', 'Greenland', 'Grenada', 'Guadeloupe', 'Guam',
        'Guatemala', 'Guernsey', 'Guinea', 'Guinea Ekuatorial', 'Guinea-Bissau',
        'Guyana', 'Guyana Prancis', 'Haiti', 'Honduras', 'Hong Kong',
        'Hongaria', 'India', 'Indonesia', 'Inggris Raya', 'Irak', 'Iran',
        'Irlandia', 'Islandia', 'Israel', 'Italia', 'Jamaika', 'Jepang',
        'Jerman', 'Jersey', 'Jordan', 'Kaledonia baru', 'Kamboja', 'Kamerun',
        'Kanada', 'Kazakhstan', 'Kenya', 'Kepulauan Cayman', 'Kepulauan Cook',
        'Kepulauan Falkland (Malvinas)', 'Kepulauan Faroe',
        'Kepulauan Mariana Utara', 'Kepulauan Marshall', 'Kepulauan Pitcairn',
        'Kepulauan Solomon',

        // http://id.wikipedia.org/wiki/United_States_Minor_Outlying_Islands
        'Kepulauan Terluar Kecil Amerika Serikat',

        'Kepulauan Turks dan Caicos',

        // http://id.wikipedia.org/wiki/United_States_Virgin_Islands
        'Kepulauan Virgin Amerika Serikat',

        'Kepulauan Virgin Inggris', 'Kiribati', 'Kolombia', 'Komoro', 'Kongo', 'Korea',
        'Korea', 'Kosta Rika', 'Kroasia',

        // http://id.wikipedia.org/wiki/Cuba
        'Kuba',

        'Kuwait', 'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libyan Arab Jamahiriya',
        'Liechtenstein', 'Lithuania', 'Luxembourg', 'Madagaskar', 'Makau', 'Makedonia',
        'Maladewa', 'Malawi', 'Malaysia', 'Mali', 'Malta', 'Maroko',

        // http://id.wikipedia.org/wiki/Martinique
        'Martinik',

        'Mauritania', 'Mauritius', 'Mayotte', 'Meksiko', 'Mesir', 'Mikronesia', 'Moldova',
        'Monako', 'Mongolia', 'Montenegro', 'Montserrat', 'Mozambik', 'Myanmar',
        'Namibia', 'Nauru', 'Nepal', 'Niger', 'Nigeria', 'Nikaragua', 'Niue', 'Norwegia',
        'Oman', 'Pakistan', 'Palau', 'Panama',

        // http://id.wikipedia.org/wiki/Cote_d%27Ivoire
        'Pantai Gading',

        'Papua Nugini', 'Paraguay', 'Peru', 'Polandia', 'Polinesia Prancis', 'Portugal',
        'Prancis', 'Puerto Rico',

        // http://ms.wikipedia.org/wiki/Pulau_Bouvet
        'Pulau Bouvet',

        // http://id.wikipedia.org/wiki/Pulau_Heard_dan_Kepulauan_McDonald
        'Pulau Heard dan Kepulauan McDonald',

        // http://id.wikipedia.org/wiki/Isle_of_Man
        'Pulau Man',

        'Pulau Natal', 'Pulau Norfolk', 'Qatar', 'Republik Afrika Tengah',
        'Republik Ceko',

        // http://id.wikipedia.org/wiki/Chad
        'Republik Chad',

        'Republik Demokratik Rakyat Laos', 'Republik Dominika', 'Republik Kirgizstan',

        // http://id.wikipedia.org/wiki/Reunion
        'Réunion',

        'Rumania', 'Rwanda', 'Sahara Barat', 'Saint Barthelemy', 'Saint Helena',
        'Saint Kitts dan Nevis', 'Saint Lucia', 'Saint Martin',
        'Saint Pierre dan Miquelon', 'Saint Vincent dan Grenadines', 'Samoa',
        'Samoa Amerika', 'San Marino', 'Sao Tome dan Principe', 'Selandia baru',
        'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapura', 'Siprus',
        'Slovakia (Republik Slovak)', 'Slovenia', 'Somalia', 'Spanyol', 'Sri Lanka',
        'Sudan', 'Suriah', 'Suriname', 'Svalbard & Jan Mayen Islands', 'Swaziland',
        'Swedia', 'Swiss', 'Taiwan', 'Tajikistan',

        // http://id.wikipedia.org/wiki/Cape_Verde
        'Tanjung Verde',

        'Tanzania', 'Thailand', 'Timor-Leste', 'Togo', 'Tokelau', 'Tonga',
        'Trinidad dan Tobago', 'Tunisia', 'Turki', 'Turkmenistan', 'Tuvalu', 'Uganda',
        'Ukraina', 'Uni Emirat Arab', 'Uruguay', 'Uzbekistan', 'Vanuatu',

        // http://id.wikipedia.org/wiki/Holy_See
        'Vatikan',

        'Venezuela', 'Vietnam', 'Wallis dan Futuna', 'Wilayah Palestina',

        // http://id.wikipedia.org/wiki/United_States_Virgin_Islands
        'Wilayah Samudra Hindia Britania',

        'Wilayah Selatan Perancis', 'Yaman', 'Yunani', 'Zambia', 'Zimbabwe'
    );

    /**
     * @link http://id.wikipedia.org/wiki/Daftar_kabupaten_dan_kota_Indonesia#Daftar_kota
     */
    protected static $cityNames = array(
        "Administrasi Jakarta Barat", "Administrasi Jakarta Pusat",
        "Administrasi Jakarta Selatan", "Administrasi Jakarta Timur",
        "Administrasi Jakarta Utara", "Ambon", "Balikpapan", "Banda Aceh",
        "Bandar Lampung", "Bandung", "Banjar", "Banjarbaru", "Banjarmasin",
        "Batam", "Batu", "Bau-Bau", "Bekasi",
        "Bengkulu", "Bima", "Binjai", "Bitung",
        "Blitar", "Bogor", "Bontang", "Bukittinggi",
        "Cilegon", "Cimahi", "Cirebon", "Denpasar",
        "Depok", "Dumai", "Gorontalo", "Gunungsitoli",
        "Jambi", "Jayapura", "Kediri", "Kendari",
        "Kotamobagu", "Kupang", "Langsa", "Lhokseumawe",
        "Lubuklinggau", "Madiun", "Magelang", "Makassar",
        "Malang", "Manado", "Mataram", "Medan",
        "Metro", "Mojokerto", "Padang", "Padangpanjang",
        "Padangsidempuan", "Pagar Alam", "Palangka Raya", "Palembang",
        "Palopo", "Palu", "Pangkal Pinang", "Parepare",
        "Pariaman", "Pasuruan", "Payakumbuh", "Pekalongan",
        "Pekanbaru", "Pematangsiantar", "Pontianak", "Prabumulih",
        "Probolinggo", "Sabang", "Salatiga", "Samarinda",
        "Sawahlunto", "Semarang", "Serang", "Sibolga",
        "Singkawang", "Solok", "Sorong", "Subulussalam",
        "Sukabumi", "Sungai Penuh", "Surabaya", "Surakarta",
        "Tangerang", "Tangerang Selatan", "Tanjung Pinang", "Tanjungbalai",
        "Tarakan", "Tasikmalaya", "Tebing Tinggi", "Tegal",
        "Ternate", "Tidore Kepulauan", "Tomohon", "Tual",
        "Yogyakarta",
    );

    protected static $cityFormats = array(
        '{{cityName}}',
    );

    protected static $streetNameFormats = array(
        '{{street}}'
    );

    protected static $streetAddressFormats = array(
        '{{streetPrefix}} {{street}} No. {{buildingNumber}}',
    );

    protected static $addressFormats = array(
        "{{streetAddress}}, {{city}} {{postcode}}, {{stateAbbr}}",
    );

    protected static $postcode = array('%####');

    /**
     * @example 'Kalimantan Tengah'
     */
    public static function state()
    {
        return static::randomElement(static::$state);
    }

    /**
     * @example 'Banten'
     */
    public static function stateAbbr()
    {
        return static::randomElement(static::$stateAbbr);
    }

    public static function streetPrefix()
    {
        return static::randomElement(static::$streetPrefix);
    }

    public static function cityName()
    {
        return static::randomElement(static::$cityNames);
    }

    public function city()
    {
        $format = static::randomElement(static::$cityFormats);

        return $this->generator->parse($format);
    }

    public static function street()
    {
        return static::randomElement(static::$street);
    }

    public static function buildingNumber()
    {
        return static::numberBetween(1, 999);
    }
}
