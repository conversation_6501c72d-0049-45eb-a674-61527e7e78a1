<?php

namespace Faker\Provider\sv_SE;

class Company extends \Faker\Provider\Company
{
    protected static $formats = array(
        '{{lastName}} {{companySuffix}}',
        '{{lastName}} {{companySuffix}}',
        '{{lastName}} {{companySuffix}}',
        '{{firstName}} {{lastName}} {{companySuffix}}',
        '{{lastName}} & {{lastName}} {{companySuffix}}',
        '{{lastName}} & {{lastName}}',
        '{{lastName}} och {{lastName}}',
        '{{lastName}} och {{lastName}} {{companySuffix}}'
    );

    protected static $companySuffix = array('AB', 'HB');
    
    protected static $jobTitles = array('Automationsingenjör', 'Bagare', 'Digital Designer', 'Ekonom', 'Ekonomichef', 'Elektronikingenjör', '<PERSON><PERSON><PERSON><PERSON><PERSON>jare', 'Försäljningschef', 'Innovationsdirektör', 'Investeringsdirektör', 'Journalist', '<PERSON><PERSON>', 'Ku<PERSON>urstrateg', '<PERSON><PERSON><PERSON>e', '<PERSON><PERSON><PERSON>rare', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>rare', 'Projektledare', 'Sjuksköterska', 'Utvecklare', 'UX Designer', 'Webbutvecklare');
    
    public function jobTitle()
    {
        return static::randomElement(static::$jobTitles);
    }
}
