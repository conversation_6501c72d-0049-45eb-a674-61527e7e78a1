# CHANGELOG

## 2019-12-03, v1.9.1

- Add link to PHPStan extension to readme [\#1834](https://github.com/fzaninotto/Faker/pull/1834) ([finwe](https://github.com/finwe))
- Enhancement: Collect code coverage [\#1824](https://github.com/fzaninotto/Faker/pull/1824) ([localheinz](https://github.com/localheinz))
- Enhancement: Use all columns when running tests [\#1823](https://github.com/fzaninotto/Faker/pull/1823) ([localheinz](https://github.com/localheinz))
- Enhancement: Configure verbose output via phpunit.xml.dist [\#1822](https://github.com/fzaninotto/Faker/pull/1822) ([localheinz](https://github.com/localheinz))
- Curly braces for arrays is deprecated in PHP 7.4 [\#1843](https://github.com/fzaninotto/Faker/pull/1843) ([wimg](https://github.com/wimg))
- Fix: Reduce visibility of setUp\(\) and tearDown\(\) [\#1821](https://github.com/fzaninotto/Faker/pull/1821) ([localheinz](https://github.com/localheinz))
- Fix: Mark test classes as final [\#1820](https://github.com/fzaninotto/Faker/pull/1820) ([localheinz](https://github.com/localheinz))
- Fix: Remove unnecessary class-level DocBlocks [\#1819](https://github.com/fzaninotto/Faker/pull/1819) ([localheinz](https://github.com/localheinz))

## 2019-11-10, v1.9.0

This will the last minor release in the `1.x` cycle.  

- Add all Iran's provinces land lines numbers [\#1806](https://github.com/fzaninotto/Faker/pull/1806) ([kingofnull](https://github.com/kingofnull))
- replace latin "B" to cyrillic "B" for uk\_UA locale [\#1800](https://github.com/fzaninotto/Faker/pull/1800) ([FI-LIFE](https://github.com/FI-LIFE))
- Add elgentos/masquerade to third-party library list [\#1798](https://github.com/fzaninotto/Faker/pull/1798) ([erikhansen](https://github.com/erikhansen))
- Add link to Gravatar provider repository [\#1796](https://github.com/fzaninotto/Faker/pull/1796) ([ottaviano](https://github.com/ottaviano))
- Add mobileNumber\(\) method for nb\_NO [\#1788](https://github.com/fzaninotto/Faker/pull/1788) ([carestad](https://github.com/carestad))
- Enhancement: Slightly clean up Company provider [\#1783](https://github.com/fzaninotto/Faker/pull/1783) ([localheinz](https://github.com/localheinz))
- Added some more information for autoloading in the readme [\#1780](https://github.com/fzaninotto/Faker/pull/1780) ([pimjansen](https://github.com/pimjansen))
- Enhancement: Add issue template for bug report [\#1779](https://github.com/fzaninotto/Faker/pull/1779) ([localheinz](https://github.com/localheinz))
- Add hslColor to ColorProvider [\#1776](https://github.com/fzaninotto/Faker/pull/1776) ([icanhazstring](https://github.com/icanhazstring))
- Enhancement: Consistently use composer package name for link content [\#1770](https://github.com/fzaninotto/Faker/pull/1770) ([localheinz](https://github.com/localheinz))
- Add Correct Thai words for streets,soi etc names and add Thai First a… [\#1769](https://github.com/fzaninotto/Faker/pull/1769) ([pierrejoye](https://github.com/pierrejoye))
- Enhancement: Assert that postcode generated by Austrian Address provider matches format [\#1766](https://github.com/fzaninotto/Faker/pull/1766) ([localheinz](https://github.com/localheinz))
- Enhancement: Update squizlabs/php\_codesniffer [\#1763](https://github.com/fzaninotto/Faker/pull/1763) ([localheinz](https://github.com/localheinz))
- Enhancement: Assert that imageUrl can be used with gray flag [\#1762](https://github.com/fzaninotto/Faker/pull/1762) ([localheinz](https://github.com/localheinz))
- Enhancement: Introduce build and help targets [\#1761](https://github.com/fzaninotto/Faker/pull/1761) ([localheinz](https://github.com/localheinz))
- Enhancement: Disable Xdebug as early as possible [\#1758](https://github.com/fzaninotto/Faker/pull/1758) ([localheinz](https://github.com/localheinz))
- Enhancement: Clean up .gitattributes [\#1756](https://github.com/fzaninotto/Faker/pull/1756) ([localheinz](https://github.com/localheinz))
- Enhancement: Reference phpunit.xsd as installed with composer [\#1755](https://github.com/fzaninotto/Faker/pull/1755) ([localheinz](https://github.com/localheinz))
- add id\_ID Color [\#1754](https://github.com/fzaninotto/Faker/pull/1754) ([cacing69](https://github.com/cacing69))
- PHP 7.4 compatibility [\#1748](https://github.com/fzaninotto/Faker/pull/1748) ([pimjansen](https://github.com/pimjansen))
- Add lastName gender specific on ru\_RU locale [\#1747](https://github.com/fzaninotto/Faker/pull/1747) ([aanfarhan](https://github.com/aanfarhan))
- Update README - Add a new faker. [\#1731](https://github.com/fzaninotto/Faker/pull/1731) ([drupol](https://github.com/drupol))
- Add faker-eddy-malou third-party library [\#1717](https://github.com/fzaninotto/Faker/pull/1717) ([Metrakit](https://github.com/Metrakit))
- \[de\_DE\] Company\JobTitle: list of common german job titles [\#1716](https://github.com/fzaninotto/Faker/pull/1716) ([amacado](https://github.com/amacado))
- Added 'red' to $safeColorNames [\#1701](https://github.com/fzaninotto/Faker/pull/1701) ([xfudox](https://github.com/xfudox))
- \[pt\_PT/Address\] Add abbreviature to 'travessa' and 'largo' [\#1606](https://github.com/fzaninotto/Faker/pull/1606) ([simaolemos](https://github.com/simaolemos))
- Fix method name for en\_NZ provider in readme [\#1595](https://github.com/fzaninotto/Faker/pull/1595) ([gdhnz](https://github.com/gdhnz))
- Refactoring of the TextTest class [\#1590](https://github.com/fzaninotto/Faker/pull/1590) ([stevegrunwell](https://github.com/stevegrunwell))
- add gender variants for last name \[lt\_LT\] [\#1586](https://github.com/fzaninotto/Faker/pull/1586) ([ikerasLT](https://github.com/ikerasLT))
- add .id TLD [\#1566](https://github.com/fzaninotto/Faker/pull/1566) ([ad3n](https://github.com/ad3n))
- Add Color provider to es\_ES locale [\#1559](https://github.com/fzaninotto/Faker/pull/1559) ([juananruiz](https://github.com/juananruiz))
- Enhancement: Keep packages sorted in composer.json [\#1551](https://github.com/fzaninotto/Faker/pull/1551) ([localheinz](https://github.com/localheinz))
- Portuguese providers for Companies and Internet [\#1537](https://github.com/fzaninotto/Faker/pull/1537) ([promatik](https://github.com/promatik))
- Improve Dutch company names [\#1529](https://github.com/fzaninotto/Faker/pull/1529) ([koole](https://github.com/koole))
- Document randomDigitNot base formatter. [\#1514](https://github.com/fzaninotto/Faker/pull/1514) ([shawnlindstrom](https://github.com/shawnlindstrom))
- Estonian names [\#1461](https://github.com/fzaninotto/Faker/pull/1461) ([much-rebel](https://github.com/much-rebel))
- add birthplace support for ID nik generator [\#1451](https://github.com/fzaninotto/Faker/pull/1451) ([mikk150](https://github.com/mikk150))
- List of banks in Brazil added [\#1427](https://github.com/fzaninotto/Faker/pull/1427) ([diegohenicka](https://github.com/diegohenicka))
- Remove last names from the male first names array \(et\_EE\) [\#1799](https://github.com/fzaninotto/Faker/pull/1799) ([Mihkel100](https://github.com/Mihkel100))
- Update out-of-date currency codes [\#1795](https://github.com/fzaninotto/Faker/pull/1795) ([Brindster](https://github.com/Brindster))
- Floor is not always returning an int [\#1790](https://github.com/fzaninotto/Faker/pull/1790) ([pimjansen](https://github.com/pimjansen))
- typo [\#1789](https://github.com/fzaninotto/Faker/pull/1789) ([enumag](https://github.com/enumag))
- Added batch inserts for doctrine orm populate [\#1781](https://github.com/fzaninotto/Faker/pull/1781) ([pimjansen](https://github.com/pimjansen))
- FIx: Avoid deprecation warning in newer CakePHP version [\#1777](https://github.com/fzaninotto/Faker/pull/1777) ([icanhazstring](https://github.com/icanhazstring))
- Fix: Consistently link to GitHub repositories [\#1774](https://github.com/fzaninotto/Faker/pull/1774) ([localheinz](https://github.com/localheinz))
- Fix: Name of package [\#1772](https://github.com/fzaninotto/Faker/pull/1772) ([localheinz](https://github.com/localheinz))
- Remove BV and HM country codes. [\#1767](https://github.com/fzaninotto/Faker/pull/1767) ([oliverpool](https://github.com/oliverpool))
- Fix: Remove sudo configuration [\#1760](https://github.com/fzaninotto/Faker/pull/1760) ([localheinz](https://github.com/localheinz))
- Fix: Allow nightly builds to fail [\#1759](https://github.com/fzaninotto/Faker/pull/1759) ([localheinz](https://github.com/localheinz))
- Fix: No need to update composer itself [\#1757](https://github.com/fzaninotto/Faker/pull/1757) ([localheinz](https://github.com/localheinz))
- duplicate first name and last name removal [\#1729](https://github.com/fzaninotto/Faker/pull/1729) ([sagautam5](https://github.com/sagautam5))
- Fix-Isreal--Israel [\#1724](https://github.com/fzaninotto/Faker/pull/1724) ([GitEvil](https://github.com/GitEvil))
- fix\(issue-1721\): add phpdoc method and properties annotations for php… [\#1722](https://github.com/fzaninotto/Faker/pull/1722) ([lugus](https://github.com/lugus))
- Typo in function PHPDoc [\#1711](https://github.com/fzaninotto/Faker/pull/1711) ([Dzhuneyt](https://github.com/Dzhuneyt))
- Fix: Add allowed type DateTime to param annotation [\#1697](https://github.com/fzaninotto/Faker/pull/1697) ([localheinz](https://github.com/localheinz))
- Calling a non static method as static not allowed. [\#1696](https://github.com/fzaninotto/Faker/pull/1696) ([wilco-1985](https://github.com/wilco-1985))
- Bug Fix en\_SG mobile number generation [\#1658](https://github.com/fzaninotto/Faker/pull/1658) ([ziming](https://github.com/ziming))
- Update ja\_JP Address Provider [\#1649](https://github.com/fzaninotto/Faker/pull/1649) ([atmngw](https://github.com/atmngw))
- Fixed realText on ja\_JP locale broken, /u missing [\#1634](https://github.com/fzaninotto/Faker/pull/1634) ([mihit](https://github.com/mihit))
- Removed comma and space from $firstNameMale for en\_IN [\#1608](https://github.com/fzaninotto/Faker/pull/1608) ([phpspider](https://github.com/phpspider))
- Prevent Transliterator class autoloading [\#1575](https://github.com/fzaninotto/Faker/pull/1575) ([MontealegreLuis](https://github.com/MontealegreLuis))
- building numbers in Germany never start with a zero or are zero [\#1530](https://github.com/fzaninotto/Faker/pull/1530) ([fostam](https://github.com/fostam))
- Fixing sk\_SK men last names set [\#1459](https://github.com/fzaninotto/Faker/pull/1459) ([pavoltanuska](https://github.com/pavoltanuska))
- Taiwan ID. The initial letter must be a capital letter. p-\>P [\#1434](https://github.com/fzaninotto/Faker/pull/1434) ([slawa-dev](https://github.com/slawa-dev))
- Revert "Added 'red' to $safeColorNames" [\#1778](https://github.com/fzaninotto/Faker/pull/1778) ([fzaninotto](https://github.com/fzaninotto))
- Added realText capability for fr\_CA [\#1686](https://github.com/fzaninotto/Faker/pull/1686) ([bobanum](https://github.com/bobanum))
- Add loremflickr.com Provider to the thrid parties [\#1683](https://github.com/fzaninotto/Faker/pull/1683) ([xvladxtremal](https://github.com/xvladxtremal))
- Add Undeclared Faker Instance to PhoneNumber Test in en\_NG [\#1682](https://github.com/fzaninotto/Faker/pull/1682) ([thearsalan](https://github.com/thearsalan))
- PSR-2 updates to readme [\#1679](https://github.com/fzaninotto/Faker/pull/1679) ([b3none](https://github.com/b3none))
- PHPDoc for `passthrough` function on Generator [\#1678](https://github.com/fzaninotto/Faker/pull/1678) ([tabakhase](https://github.com/tabakhase))
- Make en\_CA Phone Provider Extend the en\_US One [\#1676](https://github.com/fzaninotto/Faker/pull/1676) ([darrylhein](https://github.com/darrylhein))
- Add Valid National Code Generator to fa\_IR Person [\#1675](https://github.com/fzaninotto/Faker/pull/1675) ([thearsalan](https://github.com/thearsalan))
- Updated readme.md: added a missing backslash [\#1673](https://github.com/fzaninotto/Faker/pull/1673) ([aubryfr](https://github.com/aubryfr))
- Add 'Faker\Provider\en\_US\Company::catchPhrase' to ru\_RU locale [\#1668](https://github.com/fzaninotto/Faker/pull/1668) ([axklim](https://github.com/axklim))
- Update deprecated \PHPUnit\_Framework\_TestCase with PHPUnit\Framework\TestCase [\#1664](https://github.com/fzaninotto/Faker/pull/1664) ([thewhit](https://github.com/thewhit))
- Support populating Doctrine's immutable date types [\#1639](https://github.com/fzaninotto/Faker/pull/1639) ([voronkovich](https://github.com/voronkovich))
- add titleMale and titleFemale method to all Person classes [\#1635](https://github.com/fzaninotto/Faker/pull/1635) ([RiverDanceGit](https://github.com/RiverDanceGit))
- Minor typo fix [\#1631](https://github.com/fzaninotto/Faker/pull/1631) ([nhedger](https://github.com/nhedger))
- Add link to er1z/fakemock [\#1622](https://github.com/fzaninotto/Faker/pull/1622) ([er1z](https://github.com/er1z))
- Add Dutch \(nl\_NL\) job titles [\#1618](https://github.com/fzaninotto/Faker/pull/1618) ([SjorsO](https://github.com/SjorsO))
- Exclude files and folder from release archive [\#1617](https://github.com/fzaninotto/Faker/pull/1617) ([ankurk91](https://github.com/ankurk91))
- Update doc block [\#1615](https://github.com/fzaninotto/Faker/pull/1615) ([ankurk91](https://github.com/ankurk91))
- Suggest PSR-4 compliant autoloader in comment [\#1614](https://github.com/fzaninotto/Faker/pull/1614) ([samnela](https://github.com/samnela))
- Fix dateTimeBetween\(\) Argument on Generator phpdoc [\#1583](https://github.com/fzaninotto/Faker/pull/1583) ([hisomura](https://github.com/hisomura))
- Fix Dutch typo [\#1580](https://github.com/fzaninotto/Faker/pull/1580) ([gizburdt](https://github.com/gizburdt))
- Old city names changed to new ones [\#1578](https://github.com/fzaninotto/Faker/pull/1578) ([goszowski](https://github.com/goszowski))
- Correct documentation [\#1571](https://github.com/fzaninotto/Faker/pull/1571) ([rquadling](https://github.com/rquadling))
- Make $suffix protected not private in nl\_NL\Person provider [\#1561](https://github.com/fzaninotto/Faker/pull/1561) ([SpadXIII](https://github.com/SpadXIII))
- Add PicsumPhotos Provider to the thrid parties [\#1560](https://github.com/fzaninotto/Faker/pull/1560) ([bluemmb](https://github.com/bluemmb))
- fix austrian postal codes starting with invalid 0 [\#1545](https://github.com/fzaninotto/Faker/pull/1545) ([mpge](https://github.com/mpge))
- Restore a random seed when the Generator is destroyed [\#1534](https://github.com/fzaninotto/Faker/pull/1534) ([lyrixx](https://github.com/lyrixx))
- Add a swiss social security number \(AVS13\) generator [\#1533](https://github.com/fzaninotto/Faker/pull/1533) ([nhedger](https://github.com/nhedger))
- Small tweaks [\#1526](https://github.com/fzaninotto/Faker/pull/1526) ([carusogabriel](https://github.com/carusogabriel))
- fix ukraine first name person, delete space [\#1525](https://github.com/fzaninotto/Faker/pull/1525) ([Yasuslik](https://github.com/Yasuslik))
- Removed unknown postcode [\#1440](https://github.com/fzaninotto/Faker/pull/1440) ([ayanozturk](https://github.com/ayanozturk))
- Add more PhoneNumber options for es\_ES [\#1219](https://github.com/fzaninotto/Faker/pull/1219) ([driade](https://github.com/driade))
- Fix duplication in uk\_UA male first names [\#1214](https://github.com/fzaninotto/Faker/pull/1214) ([fre5h](https://github.com/fre5h))
- Added missing grayscale option/flag to Image provider [\#928](https://github.com/fzaninotto/Faker/pull/928) ([sebastianvilla](https://github.com/sebastianvilla))

## 2018-07-12, v1.8.0

- Typo in readme [\#1521](https://github.com/fzaninotto/Faker/pull/1521) ([jmhobbs](https://github.com/jmhobbs))
- Replaced Hilll with Hill [\#1516](https://github.com/fzaninotto/Faker/pull/1516) ([MarkVaughn](https://github.com/MarkVaughn))
- \[it\_IT\] Improve vat ID generated using official rules [\#1508](https://github.com/fzaninotto/Faker/pull/1508) ([mavimo](https://github.com/mavimo))
- \[hu\_HU\] Address: Fix unnecessary new line in string [\#1507](https://github.com/fzaninotto/Faker/pull/1507) ([ntomka](https://github.com/ntomka))
- add phone numer format [\#1506](https://github.com/fzaninotto/Faker/pull/1506) ([Enosh-Yu](https://github.com/Enosh-Yu))
- Fix typo in fr\_CA Provider [\#1505](https://github.com/fzaninotto/Faker/pull/1505) ([ultreson](https://github.com/ultreson))
- Add fake-car provider link [\#1497](https://github.com/fzaninotto/Faker/pull/1497) ([pelmered](https://github.com/pelmered))
- create `passthrough` function [\#1493](https://github.com/fzaninotto/Faker/pull/1493) ([browner12](https://github.com/browner12))
- update Polish bank list [\#1482](https://github.com/fzaninotto/Faker/pull/1482) ([IonBazan](https://github.com/IonBazan))
- Update the parameters to check if the setter is callable [\#1470](https://github.com/fzaninotto/Faker/pull/1470) ([rossmitchell](https://github.com/rossmitchell))
- Push the max date far into the future so the test can pass [\#1469](https://github.com/fzaninotto/Faker/pull/1469) ([rossmitchell](https://github.com/rossmitchell))
- Update Address.php [\#1465](https://github.com/fzaninotto/Faker/pull/1465) ([Saibamen](https://github.com/Saibamen))
- Turkish identity number for tr\_TR [\#1462](https://github.com/fzaninotto/Faker/pull/1462) ([aykutaras](https://github.com/aykutaras))
- Fixing rare iin with 13-digits. [\#1450](https://github.com/fzaninotto/Faker/pull/1450) ([vadimonus](https://github.com/vadimonus))
- Fix Polish PESEL faker [\#1449](https://github.com/fzaninotto/Faker/pull/1449) ([Dartui](https://github.com/Dartui))
- Adds valid 08 number formats for fr\_FR [\#1439](https://github.com/fzaninotto/Faker/pull/1439) ([ppelgrims](https://github.com/ppelgrims))
- Add YouTube provider link [\#1422](https://github.com/fzaninotto/Faker/pull/1422) ([aalaap](https://github.com/aalaap))
- Update PHPDoc of the DateTime provider. [\#1419](https://github.com/fzaninotto/Faker/pull/1419) ([tomzx](https://github.com/tomzx))
- Normalize name of variable [\#1412](https://github.com/fzaninotto/Faker/pull/1412) ([eaglewu](https://github.com/eaglewu))
- Added "blockchain" to en-us company provider catchPhrase method [\#1411](https://github.com/fzaninotto/Faker/pull/1411) ([samoldenburg](https://github.com/samoldenburg))
- Fix for Spot2 ORM EntityPopulator [\#1408](https://github.com/fzaninotto/Faker/pull/1408) ([michal-borek](https://github.com/michal-borek))
- TH color name [\#1404](https://github.com/fzaninotto/Faker/pull/1404) ([Naruedom](https://github.com/Naruedom))
- added Malaysia \[ms\_MY\] locale [\#1403](https://github.com/fzaninotto/Faker/pull/1403) ([kenfai](https://github.com/kenfai))
- Implementation of the function that generates Brazilian area codes fixed. [\#1401](https://github.com/fzaninotto/Faker/pull/1401) ([jackmiras](https://github.com/jackmiras))
- VISA retired the 13 digit PAN moved to new cardParams [\#1400](https://github.com/fzaninotto/Faker/pull/1400) ([hppycoder](https://github.com/hppycoder))
- Remove unused variable inside closure [\#1395](https://github.com/fzaninotto/Faker/pull/1395) ([carusogabriel](https://github.com/carusogabriel))
- .nz domain updates [\#1393](https://github.com/fzaninotto/Faker/pull/1393) ([xurizaemon](https://github.com/xurizaemon))
- Add licenceCode method in the to es\_ES person provider [\#1392](https://github.com/fzaninotto/Faker/pull/1392) ([ffiguereo](https://github.com/ffiguereo))
- allow `randomElements` to accept a Traversable object [\#1389](https://github.com/fzaninotto/Faker/pull/1389) ([browner12](https://github.com/browner12))
- Doc: rg remove formatting [\#1387](https://github.com/fzaninotto/Faker/pull/1387) ([emtudo](https://github.com/emtudo))
- Add numbers with start 4 [\#1386](https://github.com/fzaninotto/Faker/pull/1386) ([emtudo](https://github.com/emtudo))
- update th\_TH mobile number format [\#1385](https://github.com/fzaninotto/Faker/pull/1385) ([earthpyy](https://github.com/earthpyy))
- Translate country names for lv\_LV provider. [\#1383](https://github.com/fzaninotto/Faker/pull/1383) ([ronaldsgailis](https://github.com/ronaldsgailis))
- Clean elses [\#1382](https://github.com/fzaninotto/Faker/pull/1382) ([carusogabriel](https://github.com/carusogabriel))
- French vat formatter [\#1381](https://github.com/fzaninotto/Faker/pull/1381) ([ppelgrims](https://github.com/ppelgrims))
- Replaces rtrim with preg\_replace [\#1380](https://github.com/fzaninotto/Faker/pull/1380) ([ppelgrims](https://github.com/ppelgrims))
- Refactoring tests [\#1375](https://github.com/fzaninotto/Faker/pull/1375) ([carusogabriel](https://github.com/carusogabriel))
- Added link in readme to provider FakerRestaurant [\#1374](https://github.com/fzaninotto/Faker/pull/1374) ([jzonta](https://github.com/jzonta))
- Remove obsolete currency codes [\#1373](https://github.com/fzaninotto/Faker/pull/1373) ([tpraxl](https://github.com/tpraxl))
- \[ru\_RU\] Updated countries and added source link [\#1372](https://github.com/fzaninotto/Faker/pull/1372) ([ilyahoilik](https://github.com/ilyahoilik))
- Test against PHP 7.2 [\#1371](https://github.com/fzaninotto/Faker/pull/1371) ([carusogabriel](https://github.com/carusogabriel))
- Feature: nl\_BE text provider [\#1370](https://github.com/fzaninotto/Faker/pull/1370) ([rauwebieten](https://github.com/rauwebieten))
- default value for Payment::iban\(\) country code [\#1369](https://github.com/fzaninotto/Faker/pull/1369) ([madmanmax](https://github.com/madmanmax))
- skip test failing on bigendian [\#1365](https://github.com/fzaninotto/Faker/pull/1365) ([remicollet](https://github.com/remicollet))
- Update Person.php [\#1364](https://github.com/fzaninotto/Faker/pull/1364) ([majamusan](https://github.com/majamusan))
- Prevent errors on private methods [\#1363](https://github.com/fzaninotto/Faker/pull/1363) ([petecoop](https://github.com/petecoop))
- adds rijksregisternummer [\#1361](https://github.com/fzaninotto/Faker/pull/1361) ([ppelgrims](https://github.com/ppelgrims))
- Add secondary address to fr\_FR provider [\#1356](https://github.com/fzaninotto/Faker/pull/1356) ([nicodmf](https://github.com/nicodmf))
- Add company provider for tr\_TR [\#1355](https://github.com/fzaninotto/Faker/pull/1355) ([yuks](https://github.com/yuks))
- nb\_NO provider updates [\#1350](https://github.com/fzaninotto/Faker/pull/1350) ([alexqhj](https://github.com/alexqhj))
- only test available date range on 32-bit [\#1348](https://github.com/fzaninotto/Faker/pull/1348) ([remicollet](https://github.com/remicollet))
- Bump PHPUnit version for namespace compatibility [\#1345](https://github.com/fzaninotto/Faker/pull/1345) ([carusogabriel](https://github.com/carusogabriel))
- Use PSR-1 for PHPUnit TestCase [\#1344](https://github.com/fzaninotto/Faker/pull/1344) ([carusogabriel](https://github.com/carusogabriel))
- Fix FR\_fr 07 prefix mobile number generation [\#1343](https://github.com/fzaninotto/Faker/pull/1343) ([svanpoeck](https://github.com/svanpoeck))
- Update Text.php [\#1339](https://github.com/fzaninotto/Faker/pull/1339) ([gulaandrij](https://github.com/gulaandrij))
- Add two new company type in the Swiss Provider [\#1336](https://github.com/fzaninotto/Faker/pull/1336) ([pvullioud](https://github.com/pvullioud))
- Change symbol 'minus' with code 226 to 'minus' with code 45 [\#1333](https://github.com/fzaninotto/Faker/pull/1333) ([Negasus](https://github.com/Negasus))
- \[sl\_SI\] Created provider for Company [\#1331](https://github.com/fzaninotto/Faker/pull/1331) ([alesvaupotic](https://github.com/alesvaupotic))
- Update city name [\#1328](https://github.com/fzaninotto/Faker/pull/1328) ([s9801077](https://github.com/s9801077))
- Fix \#1305 realText in some cases breaks last character [\#1326](https://github.com/fzaninotto/Faker/pull/1326) ([iamraccoon](https://github.com/iamraccoon))
- Real Dutch postal codes [\#1323](https://github.com/fzaninotto/Faker/pull/1323) ([ametad](https://github.com/ametad))
- Added male and female titles for the en\_ZA locale [\#1321](https://github.com/fzaninotto/Faker/pull/1321) ([ViGouRCanberra](https://github.com/ViGouRCanberra))
- Add German Email Providers [\#1320](https://github.com/fzaninotto/Faker/pull/1320) ([Stoffo](https://github.com/Stoffo))
- Fix "Resource temporarily unavailable" [\#1319](https://github.com/fzaninotto/Faker/pull/1319) ([eberkund](https://github.com/eberkund))
- Introduced the ability to specify a default timezone... [\#1316](https://github.com/fzaninotto/Faker/pull/1316) ([telkins](https://github.com/telkins))
- South African licence codes [\#1315](https://github.com/fzaninotto/Faker/pull/1315) ([royalmitten](https://github.com/royalmitten))
- Fix with incorrect name city. [\#1309](https://github.com/fzaninotto/Faker/pull/1309) ([zzenmate](https://github.com/zzenmate))
- Fixed type-o in readme under section about Language specific formatters [\#1302](https://github.com/fzaninotto/Faker/pull/1302) ([espenkn](https://github.com/espenkn))
- Update Person.php [\#1298](https://github.com/fzaninotto/Faker/pull/1298) ([yappkahowe](https://github.com/yappkahowe))
- Allow children classes to access self::$suffix [\#1296](https://github.com/fzaninotto/Faker/pull/1296) ([greg0ire](https://github.com/greg0ire))
- Fix with namespace payment provider for uk\_UA [\#1293](https://github.com/fzaninotto/Faker/pull/1293) ([zzenmate](https://github.com/zzenmate))
- Update zh\_TW text provider [\#1292](https://github.com/fzaninotto/Faker/pull/1292) ([s9801077](https://github.com/s9801077))
- Fix CURL status code in ImageTest.php [\#1290](https://github.com/fzaninotto/Faker/pull/1290) ([Sanfra1407](https://github.com/Sanfra1407))
- Tax Id for companies and new formats for es\_VE [\#1287](https://github.com/fzaninotto/Faker/pull/1287) ([DIOHz0r](https://github.com/DIOHz0r))
- Added idNumber for nl\_NL [\#1283](https://github.com/fzaninotto/Faker/pull/1283) ([artorozenga](https://github.com/artorozenga))
- Feature/en us company ein [\#1273](https://github.com/fzaninotto/Faker/pull/1273) ([zachflower](https://github.com/zachflower))

## 2017-08-15, v1.7.0

- Added more Ukrainian banks [\#1271](https://github.com/fzaninotto/Faker/pull/1271) ([iamraccoon](https://github.com/iamraccoon))
- Hotfix/failing unit tests [\#1269](https://github.com/fzaninotto/Faker/pull/1269) ([zachflower](https://github.com/zachflower))
- Lock Travis-CI environment to Ubuntu Precise [\#1268](https://github.com/fzaninotto/Faker/pull/1268) ([zachflower](https://github.com/zachflower))
- Added Ukrainian job title [\#1267](https://github.com/fzaninotto/Faker/pull/1267) ([iamraccoon](https://github.com/iamraccoon))
- Add compliant en\_US SSN generator [\#1266](https://github.com/fzaninotto/Faker/pull/1266) ([zachflower](https://github.com/zachflower))
- Added more Ukrainian streets and removed irrelevant names. Added more Ukrainian mobile formats [\#1265](https://github.com/fzaninotto/Faker/pull/1265) ([iamraccoon](https://github.com/iamraccoon))
- Add Internet Format for ja\_JP. [\#1260](https://github.com/fzaninotto/Faker/pull/1260) ([itigoppo](https://github.com/itigoppo))
- rectify ISO 4217 codes [\#1258](https://github.com/fzaninotto/Faker/pull/1258) ([eidng8](https://github.com/eidng8))
- Corrected of grammar of Ukrainian middlenames and test added [\#1257](https://github.com/fzaninotto/Faker/pull/1257) ([vladbuk](https://github.com/vladbuk))
- Update ISO 4217 active codes [\#1251](https://github.com/fzaninotto/Faker/pull/1251) ([eidng8](https://github.com/eidng8))
- Update Composer File [\#1248](https://github.com/fzaninotto/Faker/pull/1248) ([vinkla](https://github.com/vinkla))
- Set capitals to false [\#1243](https://github.com/fzaninotto/Faker/pull/1243) ([Stichoza](https://github.com/Stichoza))
- Use static instead of self [\#1242](https://github.com/fzaninotto/Faker/pull/1242) ([Stichoza](https://github.com/Stichoza))
- Add VAT french format [\#1241](https://github.com/fzaninotto/Faker/pull/1241) ([baptistedonaux](https://github.com/baptistedonaux))
- Add swedish job titles [\#1234](https://github.com/fzaninotto/Faker/pull/1234) ([vinkla](https://github.com/vinkla))
- Name Simo shouldn't have comma in it [\#1230](https://github.com/fzaninotto/Faker/pull/1230) ([simoheinonen](https://github.com/simoheinonen))
- Fix: Add method annotation for ValidGenerator [\#1223](https://github.com/fzaninotto/Faker/pull/1223) ([localheinz](https://github.com/localheinz))
- Add real text for es\_ES [\#1220](https://github.com/fzaninotto/Faker/pull/1220) ([driade](https://github.com/driade))
- Fix spelling errors [\#1218](https://github.com/fzaninotto/Faker/pull/1218) ([driade](https://github.com/driade))
- Fix spelling errors [\#1217](https://github.com/fzaninotto/Faker/pull/1217) ([driade](https://github.com/driade))
- Fixes typo [\#1212](https://github.com/fzaninotto/Faker/pull/1212) ([skullboner](https://github.com/skullboner))
- Add Person::middleName for ru\_RU provider [\#1209](https://github.com/fzaninotto/Faker/pull/1209) ([JustBlackBird](https://github.com/JustBlackBird))
- Fix creditCardDetails type hint [\#1208](https://github.com/fzaninotto/Faker/pull/1208) ([jejung](https://github.com/jejung))
- Expand dictionaries for ru\_RU locale [\#1206](https://github.com/fzaninotto/Faker/pull/1206) ([pwsdotru](https://github.com/pwsdotru))
- Fix ng\_NG to en\_NG [\#1205](https://github.com/fzaninotto/Faker/pull/1205) ([raphaeldealmeida](https://github.com/raphaeldealmeida))
- Add INN and KPP support for ru\_RU locale [\#1204](https://github.com/fzaninotto/Faker/pull/1204) ([pwsdotru](https://github.com/pwsdotru))
- Remove break line on pt\_PT Address format [\#1203](https://github.com/fzaninotto/Faker/pull/1203) ([raphaeldealmeida](https://github.com/raphaeldealmeida))
- Fix syntax of phpdoc boolean property [\#1198](https://github.com/fzaninotto/Faker/pull/1198) ([pavelkovar](https://github.com/pavelkovar))
- add en\_HK provider [\#1196](https://github.com/fzaninotto/Faker/pull/1196) ([miklcct](https://github.com/miklcct))
- use secure https [\#1186](https://github.com/fzaninotto/Faker/pull/1186) ([jpuck](https://github.com/jpuck))
- Add PhoneNumberFormat for ja\_JP. [\#1185](https://github.com/fzaninotto/Faker/pull/1185) ([itigoppo](https://github.com/itigoppo))
- Fix: Add class-level method annotations for DateTime provider [\#1183](https://github.com/fzaninotto/Faker/pull/1183) ([localheinz](https://github.com/localheinz))
- Add ar\_SA Color Provider [\#1182](https://github.com/fzaninotto/Faker/pull/1182) ([alhoqbani](https://github.com/alhoqbani))
- Added uk\_UA Payment provider with bank  name generator [\#1181](https://github.com/fzaninotto/Faker/pull/1181) ([spaghettimaster](https://github.com/spaghettimaster))
- Typos [\#1177](https://github.com/fzaninotto/Faker/pull/1177) ([ankitpokhrel](https://github.com/ankitpokhrel))
- Fix XML document example [\#1176](https://github.com/fzaninotto/Faker/pull/1176) ([ankitpokhrel](https://github.com/ankitpokhrel))
- Added Emoji to Miscellaneous [\#1175](https://github.com/fzaninotto/Faker/pull/1175) ([thomasfdm](https://github.com/thomasfdm))
- Typos and doc block fixes [\#1170](https://github.com/fzaninotto/Faker/pull/1170) ([ankitpokhrel](https://github.com/ankitpokhrel))
- Rewrote deprecated `each\(\)` usage [\#1168](https://github.com/fzaninotto/Faker/pull/1168) ([hboomsma](https://github.com/hboomsma))
- Refactor text method to remove duplication [\#1163](https://github.com/fzaninotto/Faker/pull/1163) ([ankitpokhrel](https://github.com/ankitpokhrel))
- Generate valid individual identification numbers kk\_KZ [\#1161](https://github.com/fzaninotto/Faker/pull/1161) ([YerlenZhubangaliyev](https://github.com/YerlenZhubangaliyev))
- Added Address and Company \[fa\_IR\] [\#1160](https://github.com/fzaninotto/Faker/pull/1160) ([thisissorna](https://github.com/thisissorna))
- Add Peruvian DNI generator [\#1158](https://github.com/fzaninotto/Faker/pull/1158) ([jgwong](https://github.com/jgwong))
- Removed double semicolon [\#1154](https://github.com/fzaninotto/Faker/pull/1154) ([pjona](https://github.com/pjona))
- Add prefixes for nl\_NL [\#1151](https://github.com/fzaninotto/Faker/pull/1151) ([hyperized](https://github.com/hyperized))
- Separated male and female names for sr\_RS locale. [\#1144](https://github.com/fzaninotto/Faker/pull/1144) ([bogdanpet](https://github.com/bogdanpet))
- Add personal ID, VAT for zh\_TW [\#1135](https://github.com/fzaninotto/Faker/pull/1135) ([Dagolin](https://github.com/Dagolin))
- Updating ninth digit on whole country [\#1132](https://github.com/fzaninotto/Faker/pull/1132) ([gpressutto5](https://github.com/gpressutto5))
- Indian states added to en\_IN locale [\#1131](https://github.com/fzaninotto/Faker/pull/1131) ([jiveshsg](https://github.com/jiveshsg))
- Add Text provider for ro\_MD [\#1129](https://github.com/fzaninotto/Faker/pull/1129) ([wecerny](https://github.com/wecerny))
- Add strict to randomNumber example [\#1124](https://github.com/fzaninotto/Faker/pull/1124) ([leepownall](https://github.com/leepownall))
- Say Eloquent is supported [\#1123](https://github.com/fzaninotto/Faker/pull/1123) ([guidocella](https://github.com/guidocella))
- Link Eloquent Populator [\#1120](https://github.com/fzaninotto/Faker/pull/1120) ([guidocella](https://github.com/guidocella))
- Removed dead code from Luhn.php [\#1118](https://github.com/fzaninotto/Faker/pull/1118) ([Newman101](https://github.com/Newman101))
- Improve Internet::transliterate performance [\#1112](https://github.com/fzaninotto/Faker/pull/1112) ([dunglas](https://github.com/dunglas))
- fix typo [\#1109](https://github.com/fzaninotto/Faker/pull/1109) ([johannesnagl](https://github.com/johannesnagl))
- \[cs\_CZ\] Fixed Czech phone numbers [\#1108](https://github.com/fzaninotto/Faker/pull/1108) ([tomasbedrich](https://github.com/tomasbedrich))
- Update MasterCard BIN Range [\#1103](https://github.com/fzaninotto/Faker/pull/1103) ([andysnell](https://github.com/andysnell))
- Add biggest german cities [\#1102](https://github.com/fzaninotto/Faker/pull/1102) ([Konafets](https://github.com/Konafets))
- Change postal code format for ko\_KR [\#1094](https://github.com/fzaninotto/Faker/pull/1094) ([coozplz](https://github.com/coozplz))
- Introduced the ability to specify the timezone for dateTimeThis\*\(\) methods [\#1090](https://github.com/fzaninotto/Faker/pull/1090) ([telkins](https://github.com/telkins))
- Fixed Issue \#1086 [\#1088](https://github.com/fzaninotto/Faker/pull/1088) ([Newman101](https://github.com/Newman101))
- \[ja\_JP\]kana of Japanese name by gender. [\#1087](https://github.com/fzaninotto/Faker/pull/1087) ([itigoppo](https://github.com/itigoppo))
- Fix unused code [\#1083](https://github.com/fzaninotto/Faker/pull/1083) ([borgogelli](https://github.com/borgogelli))
- Amended permissions for en\_GB AddressTest.php [\#1071](https://github.com/fzaninotto/Faker/pull/1071) ([Newman101](https://github.com/Newman101))
- Ensure unique IDs in randomHtml [\#1068](https://github.com/fzaninotto/Faker/pull/1068) ([vlakoff](https://github.com/vlakoff))
- Updated \[de\_DE\] city names [\#1067](https://github.com/fzaninotto/Faker/pull/1067) ([plxx](https://github.com/plxx))
- Update method signature in Generator phpdoc [\#1066](https://github.com/fzaninotto/Faker/pull/1066) ([vlakoff](https://github.com/vlakoff))
- Add Thai providers [\#1065](https://github.com/fzaninotto/Faker/pull/1065) ([tuwannu](https://github.com/tuwannu))
- \(Minor\) Fixed the default locale stated in the readme [\#1064](https://github.com/fzaninotto/Faker/pull/1064) ([taylankasap](https://github.com/taylankasap))
- \[nl\_NL\] Make person provider behave more realistically [\#1061](https://github.com/fzaninotto/Faker/pull/1061) ([curry684](https://github.com/curry684))
- Add allowDuplicates option to randomElements\(\) [\#1060](https://github.com/fzaninotto/Faker/pull/1060) ([vlakoff](https://github.com/vlakoff))
- Docblocks: Add some missing @method tags [\#1059](https://github.com/fzaninotto/Faker/pull/1059) ([Kurre](https://github.com/Kurre))
- \[fi\_FI\] Improve phone number generator [\#1054](https://github.com/fzaninotto/Faker/pull/1054) ([Kurre](https://github.com/Kurre))
- Add personalIdentityNumber\(\) to fi\_FI/Person.php [\#1053](https://github.com/fzaninotto/Faker/pull/1053) ([oittaa](https://github.com/oittaa))
- Issue \#1041 [\#1052](https://github.com/fzaninotto/Faker/pull/1052) ([daleattree](https://github.com/daleattree))
- Update Text.php [\#1051](https://github.com/fzaninotto/Faker/pull/1051) ([gulaandrij](https://github.com/gulaandrij))
- Fix French phone numbers with 07 prefix [\#1046](https://github.com/fzaninotto/Faker/pull/1046) ([fzaninotto](https://github.com/fzaninotto))
- \[Generator.php\] mt\_rand\(\) changed in PHP 7.1 [\#1045](https://github.com/fzaninotto/Faker/pull/1045) ([oittaa](https://github.com/oittaa))
- Add 'FI' to Payment Provider [\#1044](https://github.com/fzaninotto/Faker/pull/1044) ([oittaa](https://github.com/oittaa))
- Added id number generator to Person Provider for the en\_ZA locale [\#1039](https://github.com/fzaninotto/Faker/pull/1039) ([smithandre](https://github.com/smithandre))
- \[Feature\] Add nigerian provider [\#1030](https://github.com/fzaninotto/Faker/pull/1030) ([elchroy](https://github.com/elchroy))
- \[pl\_PL\] Handle state. [\#1029](https://github.com/fzaninotto/Faker/pull/1029) ([piotrooo](https://github.com/piotrooo))
- Fixed polish text - change '--' into '-'. [\#1027](https://github.com/fzaninotto/Faker/pull/1027) ([piotrooo](https://github.com/piotrooo))
- Update Text.php [\#1025](https://github.com/fzaninotto/Faker/pull/1025) ([gulaandrij](https://github.com/gulaandrij))
- Adding Nationalized Citizens to DNI in Person.php [\#1021](https://github.com/fzaninotto/Faker/pull/1021) ([celisflen-bers](https://github.com/celisflen-bers))
- Add nik to indonesia [\#1019](https://github.com/fzaninotto/Faker/pull/1019) ([Nuffic](https://github.com/Nuffic))
- fix mb\_substr missing parameter error when generating japanese string with realText method [\#1018](https://github.com/fzaninotto/Faker/pull/1018) ([horan-geeker](https://github.com/horan-geeker))
- IBAN Formatters for New Locales [\#1015](https://github.com/fzaninotto/Faker/pull/1015) ([okj579](https://github.com/okj579))
- German Bank Names [\#1014](https://github.com/fzaninotto/Faker/pull/1014) ([okj579](https://github.com/okj579))
- Adding countries for pl\_PL provider [\#1009](https://github.com/fzaninotto/Faker/pull/1009) ([mertcanesen](https://github.com/mertcanesen))
- Adding Pattern Lab plugin to list of 3rd party libraries [\#1008](https://github.com/fzaninotto/Faker/pull/1008) ([EvanLovely](https://github.com/EvanLovely))
- Korea top 100 lastName [\#1006](https://github.com/fzaninotto/Faker/pull/1006) ([tael](https://github.com/tael))
- Use real Belgian postcodes instead of random number [\#1004](https://github.com/fzaninotto/Faker/pull/1004) ([toonevdb](https://github.com/toonevdb))
- Add bankAccountNumber implementations [\#1000](https://github.com/fzaninotto/Faker/pull/1000) ([akramfares](https://github.com/akramfares))
- Generates a random NIR number \(fr\_FR\) [\#997](https://github.com/fzaninotto/Faker/pull/997) ([Ultim4T0m](https://github.com/Ultim4T0m))
- \#989 Fix country typo [\#996](https://github.com/fzaninotto/Faker/pull/996) ([adriantombu](https://github.com/adriantombu))
- adding back CNP [\#988](https://github.com/fzaninotto/Faker/pull/988) ([the-noob](https://github.com/the-noob))
- Fix phpunit tests fail on 64-bit systems \#982 [\#983](https://github.com/fzaninotto/Faker/pull/983) ([Powerhead13](https://github.com/Powerhead13))
- Remove trailing dot in username if any [\#975](https://github.com/fzaninotto/Faker/pull/975) ([vlakoff](https://github.com/vlakoff))
- HTML Lorem [\#971](https://github.com/fzaninotto/Faker/pull/971) ([rudkjobing](https://github.com/rudkjobing))
- Fix a mixup between male and female last names in Icelandic. [\#970](https://github.com/fzaninotto/Faker/pull/970) ([arthur-olafsson](https://github.com/arthur-olafsson))
- Remove duplicate [\#969](https://github.com/fzaninotto/Faker/pull/969) ([mijgame](https://github.com/mijgame))
- fix \[zh\_CN\]PhoneNumber illegal operator prefix [\#966](https://github.com/fzaninotto/Faker/pull/966) ([zhwei](https://github.com/zhwei))
- es\_ES: Generate VAT Number [\#964](https://github.com/fzaninotto/Faker/pull/964) ([miguelgf](https://github.com/miguelgf))
- Update Image.php [\#963](https://github.com/fzaninotto/Faker/pull/963) ([gulaandrij](https://github.com/gulaandrij))
- Remove cnp formatter from RO\_ro locale \(fails tests\) [\#962](https://github.com/fzaninotto/Faker/pull/962) ([fzaninotto](https://github.com/fzaninotto))
- Adding valid en\_GB postcodes [\#961](https://github.com/fzaninotto/Faker/pull/961) ([the-noob](https://github.com/the-noob))
- Adding Text for ro\_RO [\#959](https://github.com/fzaninotto/Faker/pull/959) ([the-noob](https://github.com/the-noob))
- Minor: Fixed trailing space in DateTime provider [\#956](https://github.com/fzaninotto/Faker/pull/956) ([tifabien](https://github.com/tifabien))
- Remove 'Stripper' from en\_US job titles [\#954](https://github.com/fzaninotto/Faker/pull/954) ([amcsi](https://github.com/amcsi))
- fix 32bits issue [\#953](https://github.com/fzaninotto/Faker/pull/953) ([remicollet](https://github.com/remicollet))
- Fix EAN8 checkSum generator [\#951](https://github.com/fzaninotto/Faker/pull/951) ([MatthieuMota](https://github.com/MatthieuMota))
- Fixed description and the use of early undocumented parameters. [\#949](https://github.com/fzaninotto/Faker/pull/949) ([andrey-helldar](https://github.com/andrey-helldar))
- Pushing new mobile prefixes in philippines [\#944](https://github.com/fzaninotto/Faker/pull/944) ([napoleon101392](https://github.com/napoleon101392))
- Update Company.php [\#943](https://github.com/fzaninotto/Faker/pull/943) ([thiagotalma](https://github.com/thiagotalma))
- Fix to Issue \#935 - German Locale [\#936](https://github.com/fzaninotto/Faker/pull/936) ([Newman101](https://github.com/Newman101))
- el\_CY Locale [\#930](https://github.com/fzaninotto/Faker/pull/930) ([softius](https://github.com/softius))
- Add phpdoc method dateTimeInInterval in Generator.php [\#926](https://github.com/fzaninotto/Faker/pull/926) ([KeithYeh](https://github.com/KeithYeh))
- Harmonize fr\_\*\Company [\#918](https://github.com/fzaninotto/Faker/pull/918) ([Max13](https://github.com/Max13))
- Fix: fix invalid parameter of mb\_substr\(\) [\#917](https://github.com/fzaninotto/Faker/pull/917) ([tkawaji](https://github.com/tkawaji))
- kk\_KZ Company/person identification numbers unit tests [\#916](https://github.com/fzaninotto/Faker/pull/916) ([YerlenZhubangaliyev](https://github.com/YerlenZhubangaliyev))
- ka\_GE: overall improvements to ka\_GE locale [\#913](https://github.com/fzaninotto/Faker/pull/913) ([hertzg](https://github.com/hertzg))
- Fix: Do not pick a random float less than minimum [\#909](https://github.com/fzaninotto/Faker/pull/909) ([localheinz](https://github.com/localheinz))
- Fix: Prefer dependencies installed from dist [\#908](https://github.com/fzaninotto/Faker/pull/908) ([localheinz](https://github.com/localheinz))
- Add a building number with letter to German speaking locales. [\#903](https://github.com/fzaninotto/Faker/pull/903) ([markuspoerschke](https://github.com/markuspoerschke))
- \[RFR\] Remove parts of the hu\_HU address formatters [\#902](https://github.com/fzaninotto/Faker/pull/902) ([fzaninotto](https://github.com/fzaninotto))
- use Luhn to calculate ar\_SA id numbers. [\#875](https://github.com/fzaninotto/Faker/pull/875) ([FooBarQuaxx](https://github.com/FooBarQuaxx))
- Fix Doctrine ODM Support [\#489](https://github.com/fzaninotto/Faker/pull/489) ([cbourgois](https://github.com/cbourgois))


## 2016-04-29, v1.6.0

- Remove parts of the Hungarian (hu\_HU) address formatters [\#902](https://github.com/fzaninotto/Faker/pull/902) ([fzaninotto](https://github.com/fzaninotto))
- Renamed norwegian (nb\_NO) locale [\#901](https://github.com/fzaninotto/Faker/pull/901) ([fzaninotto](https://github.com/fzaninotto))
- Improveed German (de\_DE) titles [\#897](https://github.com/fzaninotto/Faker/pull/897) ([christianbartels](https://github.com/christianbartels))
- Added VAT formatter to nl\_BE and fr\_BE providers [\#896](https://github.com/fzaninotto/Faker/pull/896) ([anvanza](https://github.com/anvanza))
- Fixed provider namespace for Lithuanian (lt\_LT) [\#894](https://github.com/fzaninotto/Faker/pull/894) ([sanis](https://github.com/sanis))
- Removed unnecessary (and incompatible) license from Russian and Ukrainian (uk\_UA & ru\_RU) Text providers [\#892](https://github.com/fzaninotto/Faker/pull/892) ([Newman101](https://github.com/Newman101))
- Improved `languageCode` formatted to include all ISO-639-1 standard codes [\#889](https://github.com/fzaninotto/Faker/pull/889) ([andrewnicols](https://github.com/andrewnicols))
- Improved Hungarian provider [\#883](https://github.com/fzaninotto/Faker/pull/883) ([balping](https://github.com/balping))
- Fixed typo in Austrian Person provider [\#880](https://github.com/fzaninotto/Faker/pull/880) ([xelan](https://github.com/xelan))
- Added Chines (zh\_CN) `catchPhrase` formatter [\#878](https://github.com/fzaninotto/Faker/pull/878) ([z-song](https://github.com/z-song))
- Added mention of Brazilian (pt\_BR) providers in readme [\#877](https://github.com/fzaninotto/Faker/pull/877) ([iget-master](https://github.com/iget-master))
- Updated composer `require` section to allow PHP 7 in safer way [\#874](https://github.com/fzaninotto/Faker/pull/874) ([TomasVotruba](https://github.com/TomasVotruba))
- Added Greek (el\_GR) `mobilePhoneNumber` and `tollFreeNumber` formatters [\#869](https://github.com/fzaninotto/Faker/pull/869) ([sebdesign](https://github.com/sebdesign))
- Added Lorempixel check for `ImageTest.php` to avoid test fails when the service is offline [\#866](https://github.com/fzaninotto/Faker/pull/866) ([Newman101](https://github.com/Newman101))
- Added Chinese (zh\_CN) Providers [\#864](https://github.com/fzaninotto/Faker/pull/864) ([z-song](https://github.com/z-song))
- Added unit tests for Canadian (en\_CA) provider [\#862](https://github.com/fzaninotto/Faker/pull/862) ([Newman101](https://github.com/Newman101))
- Added Dutch BTW \(vat\) Number [\#861](https://github.com/fzaninotto/Faker/pull/861) ([LauLaman](https://github.com/LauLaman))
- Improved Australian (en\_AU) provider [\#858](https://github.com/fzaninotto/Faker/pull/858) ([Newman101](https://github.com/Newman101))
- Added Propel2 ORM support [\#852](https://github.com/fzaninotto/Faker/pull/852) ([iTechDhaval](https://github.com/iTechDhaval))
- Added en\_IN unit test for `Address.php` [\#849](https://github.com/fzaninotto/Faker/pull/849) ([Newman101](https://github.com/Newman101))
- Updated docs to clarify that `randomElements` does not repeat input elements [\#848](https://github.com/fzaninotto/Faker/pull/848) ([sustmi](https://github.com/sustmi))
- Optimized Taiwanese (zh\_TW) `realText` provider [\#844](https://github.com/fzaninotto/Faker/pull/844) ([Newman101](https://github.com/Newman101))
- Added more Iranian (fa\_IR) TLDs [\#843](https://github.com/fzaninotto/Faker/pull/843) ([VagrantStory](https://github.com/VagrantStory))
- Added Hebrew (he\_IL) `country` formatter [\#841](https://github.com/fzaninotto/Faker/pull/841) ([yonirom](https://github.com/yonirom))
- Documented `boolean` formatter [\#840](https://github.com/fzaninotto/Faker/pull/840) ([danieliancu](https://github.com/danieliancu))
- Fixed modifiers anchor readme [\#838](https://github.com/fzaninotto/Faker/pull/838) ([danieliancu](https://github.com/danieliancu))
- Added Dutch (nl\_NL) real text provider [\#837](https://github.com/fzaninotto/Faker/pull/837) ([endroid](https://github.com/endroid))
- Added `valid` modifier [\#836](https://github.com/fzaninotto/Faker/pull/836) ([fzaninotto](https://github.com/fzaninotto))
- Added Iranian (fa\_IR) `PhoneNumber` provider [\#833](https://github.com/fzaninotto/Faker/pull/833) ([ghost](https://github.com/ghost))
- Add Brazilian (pt\_BR) `region` and `regionAbbr` formatters [\#828](https://github.com/fzaninotto/Faker/pull/828) ([francinaldo](https://github.com/francinaldo))
- Improved Austrian (de\_AT) names, states, and realtext [\#826](https://github.com/fzaninotto/Faker/pull/826) ([Findus23](https://github.com/Findus23))
- Improved German (de\_DE) names [\#825](https://github.com/fzaninotto/Faker/pull/825) ([Findus23](https://github.com/Findus23))
- Improved Latvian (lv\_LV) first names [\#823](https://github.com/fzaninotto/Faker/pull/823) ([veisis](https://github.com/veisis))
- Improved Latvian (lv\_LV) `phoneNumber` formatter [\#822](https://github.com/fzaninotto/Faker/pull/822) ([veisis](https://github.com/veisis))
- Updated phpDoc link to IBAN format reference in `Payment` provider [\#821](https://github.com/fzaninotto/Faker/pull/821) ([god107](https://github.com/god107))
- Updated Sport ORM populator to populate values for numeric fields [\#820](https://github.com/fzaninotto/Faker/pull/820) ([urisavka](https://github.com/urisavka))
- Updated Chinese (zh\_CN) operators' phone number prefix. [\#819](https://github.com/fzaninotto/Faker/pull/819) ([vistart](https://github.com/vistart))
- Optimized Spot ORM `EntityPopulator` [\#817](https://github.com/fzaninotto/Faker/pull/817) ([Newman101](https://github.com/Newman101))
- Added Korean (ko\_KR) `realText` formatter [\#815](https://github.com/fzaninotto/Faker/pull/815) ([jdssem](https://github.com/jdssem))
- Updated `imageUrl` formatter phpDoc [\#814](https://github.com/fzaninotto/Faker/pull/814) ([jonwurtzler](https://github.com/jonwurtzler))
- Optimized Taiwanese (zh\_TW) text provider [\#809](https://github.com/fzaninotto/Faker/pull/809) ([BePsvPT](https://github.com/BePsvPT))
- Added strict comparison to Czech (cs\_CS) `birthNumber` formatter [\#807](https://github.com/fzaninotto/Faker/pull/807) ([Newman101](https://github.com/Newman101))
- Added Greek (el\_GR) `realText` formatter [\#805](https://github.com/fzaninotto/Faker/pull/805) ([hootlex](https://github.com/hootlex))
- Added Simplified Chinese \(zh\_CN\) `state` and `stateAbbr` formatters [\#804](https://github.com/fzaninotto/Faker/pull/804) ([zhanghuanchong](https://github.com/zhanghuanchong))
- Update `Image` provider to allow generation of grayscale images [\#801](https://github.com/fzaninotto/Faker/pull/801) ([neutralrockets](https://github.com/neutralrockets))
- Fixed Taiwanese (zh_TW) incorrect `mb_substr()` arguments [\#799](https://github.com/fzaninotto/Faker/pull/799) ([BePsvPT](https://github.com/BePsvPT))
- Added Spot ORM populator [\#796](https://github.com/fzaninotto/Faker/pull/796) ([urisavka](https://github.com/urisavka))
- Added Italian (it\_IT) `vatId` and `taxId` formatters [\#790](https://github.com/fzaninotto/Faker/pull/790) ([brainrepo](https://github.com/brainrepo))
- Added some fixes to Armenian (hy\_AM) locale [\#788](https://github.com/fzaninotto/Faker/pull/788) ([mhamlet](https://github.com/mhamlet))
- Removed duplicate entries in `toAscii()` transliteration table, used in `Internet` provider [\#787](https://github.com/fzaninotto/Faker/pull/787) ([vlakoff](https://github.com/vlakoff))
- Added Indian (en\_IN) providers [\#785](https://github.com/fzaninotto/Faker/pull/785) ([kartiksomani](https://github.com/kartiksomani))
- Removed duplicate country names in various locales, removed non-random country arrays [\#784](https://github.com/fzaninotto/Faker/pull/784) ([fzaninotto](https://github.com/fzaninotto))
- Improved Swiss (de\_CH) phone numbers [\#782](https://github.com/fzaninotto/Faker/pull/782) ([z38](https://github.com/z38))
- Added Swiss (de\_CH) names [\#781](https://github.com/fzaninotto/Faker/pull/781) ([z38](https://github.com/z38))
- Make capitalization of first word optional in Text Provider [\#778](https://github.com/fzaninotto/Faker/pull/778) ([LagunaJavier](https://github.com/LagunaJavier))
- Added Georgian (ka\_GE) providers [\#777](https://github.com/fzaninotto/Faker/pull/777) ([akalongman](https://github.com/akalongman))
- Fix CakePHP populator [\#776](https://github.com/fzaninotto/Faker/pull/776) ([daniel-mueller](https://github.com/daniel-mueller))
- Added unit tests for `Address` provider in many locales [\#775](https://github.com/fzaninotto/Faker/pull/775) [\#773](https://github.com/fzaninotto/Faker/pull/773) [\#772](https://github.com/fzaninotto/Faker/pull/772) [\#767](https://github.com/fzaninotto/Faker/pull/767) [\#765](https://github.com/fzaninotto/Faker/pull/765) [\#764](https://github.com/fzaninotto/Faker/pull/764) [\#758](https://github.com/fzaninotto/Faker/pull/758) [\#756](https://github.com/fzaninotto/Faker/pull/756) [\#747](https://github.com/fzaninotto/Faker/pull/747) [\#741](https://github.com/fzaninotto/Faker/pull/741) ([Newman101](https://github.com/Newman101))
- Added `dbi` formatter to Spanish (es\_ES) Person provider [\#763](https://github.com/fzaninotto/Faker/pull/763) ([mikk150](https://github.com/mikk150))
- Added South Africa (en\_ZA) locale [\#761](https://github.com/fzaninotto/Faker/pull/761) ([smithandre](https://github.com/smithandre)) [\#760](https://github.com/fzaninotto/Faker/pull/760) ([smithandre](https://github.com/smithandre)) [\#759](https://github.com/fzaninotto/Faker/pull/759) ([smithandre](https://github.com/smithandre))
- Added E.164 phone number generator [\#753](https://github.com/fzaninotto/Faker/pull/753) ([daleattree](https://github.com/daleattree))
- Fixed serialization issue in `unique` modifier [\#749](https://github.com/fzaninotto/Faker/pull/749) ([EmanueleMinotto](https://github.com/EmanueleMinotto))
- Added Switzerland (de\_CH, fr\_CH, it\_CH) providers [\#739](https://github.com/fzaninotto/Faker/pull/739) ([r3h6](https://github.com/r3h6))
- Added PHPDocs, removed unused variable [\#738](https://github.com/fzaninotto/Faker/pull/738) ([daniel-mueller](https://github.com/daniel-mueller))
- Fixed building numbers to have non-zero first bumber [\#737](https://github.com/fzaninotto/Faker/pull/737) ([jmauerhan](https://github.com/jmauerhan))
- Updated ninth digit for Brazilian cell phone numbers [\#734](https://github.com/fzaninotto/Faker/pull/734) ([igorsantos07](https://github.com/igorsantos07))
- Simplified Factory code [\#732](https://github.com/fzaninotto/Faker/pull/732) ([vlakoff](https://github.com/vlakoff))
- Added mention of [images-generator](https://github.com/bruceheller/images-generator) in readme [\#731](https://github.com/fzaninotto/Faker/pull/731) ([bruceheller](https://github.com/bruceheller))
- Optimize Internet::toAscii\(\) by using a static cache and translitteration [\#730](https://github.com/fzaninotto/Faker/pull/730) [\#729](https://github.com/fzaninotto/Faker/pull/729)
[\#725](https://github.com/fzaninotto/Faker/pull/725) [\#724](https://github.com/fzaninotto/Faker/pull/724) ([vlakoff](https://github.com/vlakoff))
- Added more English (en\_GB) Phone Number formats [\#721](https://github.com/fzaninotto/Faker/pull/721) ([nickwebcouk](https://github.com/nickwebcouk))
- Cleaned up `use` statements across the code [\#719](https://github.com/fzaninotto/Faker/pull/719) ([pomaxa](https://github.com/pomaxa))
- Fixed CackePHP populator [\#718](https://github.com/fzaninotto/Faker/pull/718) ([sdustinh](https://github.com/sdustinh))
- Cleaned up various phpmd notices [\#715](https://github.com/fzaninotto/Faker/pull/715) ([pomaxa](https://github.com/pomaxa))
- Added `Color` provider to Latvian (lv_LV) locale [\#714](https://github.com/fzaninotto/Faker/pull/714) ([pomaxa](https://github.com/pomaxa))
- Fixed bad randomization in Doctrine populator [\#713](https://github.com/fzaninotto/Faker/pull/713) ([pomaxa](https://github.com/pomaxa))
- Added Mongolian (mn\_MN) providers [\#709](https://github.com/fzaninotto/Faker/pull/709) ([selmonal](https://github.com/selmonal))
- Improved Australian (en\_AU) `postcode` formatter [\#703](https://github.com/fzaninotto/Faker/pull/703) ([xfxf](https://github.com/xfxf))
- Added support for asterisks in `bothify` and `optimize` [\#701](https://github.com/fzaninotto/Faker/pull/701) ([nineinchnick](https://github.com/nineinchnick))
- Fixed important distinction between ORM and database framework in README’s reference to an external Faker provider for POMM that I have never even tested. Anyway, POMM is highly recommended if you are a Postgres fan, or if you want to please Grégoire and help him finish his lifelong project of listening to music on a hi-fi audio equipment he built from his own hands [\#696](https://github.com/fzaninotto/Faker/pull/696) ([chanmix51](https://github.com/chanmix51))
- Fixed example `text()` output in README [\#694](https://github.com/fzaninotto/Faker/pull/694) ([vlakoff](https://github.com/vlakoff))
- Added mention of CakePHP 2.x Seeder Plugin to readme [\#691](https://github.com/fzaninotto/Faker/pull/691) ([ravage84](https://github.com/ravage84))
- Fixed invalid email bug for Korean (ko\_KR) [\#690](https://github.com/fzaninotto/Faker/pull/690) ([pearlc](https://github.com/pearlc))
- Removed an invalid Dutch (nl\_NL) lastname that breaks email generator [\#689](https://github.com/fzaninotto/Faker/pull/689) ([SpaceK33z](https://github.com/SpaceK33z))
- Updated `numberBetween()` to be order agnostic [\#683](https://github.com/fzaninotto/Faker/pull/683) ([xfxf](https://github.com/xfxf))
- Added several English (en\_US) bank-related formatters [\#682](https://github.com/fzaninotto/Faker/pull/682) ([okj579](https://github.com/okj579))
- Fixed `ipv4` formatter to avoid generating special purpose addresses [\#681](https://github.com/fzaninotto/Faker/pull/681) ([ravage84](https://github.com/ravage84))
- Moved `intl` extension to `require-dev` in `composer.json` file [\#680](https://github.com/fzaninotto/Faker/pull/680) ([jaschweder](https://github.com/jaschweder))
- Added more Turkish (tr\_TR) phones number formats [\#678](https://github.com/fzaninotto/Faker/pull/678) ([Quanthir](https://github.com/Quanthir))
- Fixed primary Key warning in CakePHP ORM populator [\#677](https://github.com/fzaninotto/Faker/pull/677) ([davidyell](https://github.com/davidyell))
- Added time zone support for provider methods returning DateTime instance [\#675](https://github.com/fzaninotto/Faker/pull/675) ([bishopb](https://github.com/bishopb))
- Removed trailing spaces from some Argentinian (es\_AR) female first names [\#674](https://github.com/fzaninotto/Faker/pull/674) ([ivanmirson](https://github.com/ivanmirson))
- Added Lithuanian (lt\_LT) locale [\#673](https://github.com/fzaninotto/Faker/pull/673) ([ekateiva](https://github.com/ekateiva))
- Added mention of Alice to readme [\#665](https://github.com/fzaninotto/Faker/pull/665) ([Seldaek](https://github.com/Seldaek))
- Fixed namespace in tests [\#663](https://github.com/fzaninotto/Faker/pull/663) ([localheinz](https://github.com/localheinz))
- Fixed trailing spaces in `Color` provider [\#662](https://github.com/fzaninotto/Faker/pull/662) ([apsylone](https://github.com/apsylone))
- Removed duplicate country names in Russian (ru\_RU) `Address` provider [\#659](https://github.com/fzaninotto/Faker/pull/659) ([nurolopher](https://github.com/nurolopher))
- Added `rgba` formatter to `Color` provider [\#653](https://github.com/fzaninotto/Faker/pull/653) ([apsylone](https://github.com/apsylone))
- Fixed bad randomization in CakePHP populator [\#648](https://github.com/fzaninotto/Faker/pull/648) ([jadb](https://github.com/jadb))
- Updated phpunit configuration to better use colors [\#643](https://github.com/fzaninotto/Faker/pull/643) ([localheinz](https://github.com/localheinz))
- Updated `makefile` to install dev dependencies by default [\#642](https://github.com/fzaninotto/Faker/pull/642) ([localheinz](https://github.com/localheinz))
- Updated Travis configuration to cache dependencies between builds [\#641](https://github.com/fzaninotto/Faker/pull/641) ([localheinz](https://github.com/localheinz))
- Added SVG badge to readme for displaying Travis build status [\#640](https://github.com/fzaninotto/Faker/pull/640) ([localheinz](https://github.com/localheinz))
- Added Croatian (hr\_HR) locale [\#638](https://github.com/fzaninotto/Faker/pull/638) ([toniperic](https://github.com/toniperic))
- Updated `dateTimeBetween` PHPDoc [\#635](https://github.com/fzaninotto/Faker/pull/635) ([theofidry](https://github.com/theofidry))
- Add mention of Symfony2 bundles in readme [\#634](https://github.com/fzaninotto/Faker/pull/634) ([theofidry](https://github.com/theofidry))
- Added Hebrew (he\_IL) locale [\#633](https://github.com/fzaninotto/Faker/pull/633) ([yonirom](https://github.com/yonirom))
- Updated `seed` to accept non-integer seeds [\#632](https://github.com/fzaninotto/Faker/pull/632) ([theofidry](https://github.com/theofidry))
- Added DocBlock to `Factory::create()` [\#631](https://github.com/fzaninotto/Faker/pull/631) ([tonynelson19](https://github.com/tonynelson19))
- Added `jobTitle` generator [\#630](https://github.com/fzaninotto/Faker/pull/630) ([gregoryduckworth](https://github.com/gregoryduckworth))
- Updated Chinese (zh\_CN) `Person` provider to generate more correct names [\#628](https://github.com/fzaninotto/Faker/pull/628) ([phoenixgao](https://github.com/phoenixgao))
- Updated Brazilian (pt\_BR) `cellphone` formatter to make it more flexible [\#623](https://github.com/fzaninotto/Faker/pull/623) ([igorsantos07](https://github.com/igorsantos07))
- Add Arabic for Saudi Arabia (ar\_SA) locale [\#618](https://github.com/fzaninotto/Faker/pull/618) ([ibrasho](https://github.com/ibrasho))
- Updated en\_US phone numbers [\#615](https://github.com/fzaninotto/Faker/pull/615) ([okj579](https://github.com/okj579))
- Fixed typos in variable names and exceptions [\#614](https://github.com/fzaninotto/Faker/pull/614) ([pborreli](https://github.com/pborreli))
- Added a table of contents to the readme file. [\#613](https://github.com/fzaninotto/Faker/pull/613) ([camilopayan](https://github.com/camilopayan))
- Added Brazilian (es_BR) credit card formatters [\#608](https://github.com/fzaninotto/Faker/pull/608) ([igorsantos07](https://github.com/igorsantos07))
- Updated `iban` formatter to be cross-locale [\#607](https://github.com/fzaninotto/Faker/pull/607) ([okj579](https://github.com/okj579))
- Improved ORM name guesser logic [\#606](https://github.com/fzaninotto/Faker/pull/606) ([watermanio](https://github.com/watermanio))
- Fixed doc typo [\#605](https://github.com/fzaninotto/Faker/pull/605) ([igorsantos07](https://github.com/igorsantos07))
- Removed executable bits [\#593](https://github.com/fzaninotto/Faker/pull/593) ([siwinski](https://github.com/siwinski))
- Fixed `iban` generator [\#590](https://github.com/fzaninotto/Faker/pull/590) ([okj579](https://github.com/okj579))
- Added Philippines (en\_PH) `mobileNumber` formatter [\#589](https://github.com/fzaninotto/Faker/pull/589) ([lozadaOmr](https://github.com/lozadaOmr))
- Added support for min / max params in `latitude` and `longitude` formatters [\#570](https://github.com/fzaninotto/Faker/pull/570) ([actuallymab](https://github.com/actuallymab))
- Added Czech (cs_CZ) `birthNumber` formatter [\#535](https://github.com/fzaninotto/Faker/pull/535) ([tomasbedrich](https://github.com/tomasbedrich))
- Added `dateTimeInInterval` formatter [\#526](https://github.com/fzaninotto/Faker/pull/526) ([nicodmf](https://github.com/nicodmf))
- Updated `optional` and `boolean` apis to be more consistent [\#513](https://github.com/fzaninotto/Faker/pull/513) ([EmanueleMinotto](https://github.com/EmanueleMinotto))
- Added Greek (el\_GR) `Address` provider [\#504](https://github.com/fzaninotto/Faker/pull/504) ([drakakisgeo](https://github.com/drakakisgeo))

## 2015-05-29, v1.5.0

* Added ability to print custom text on the images fetched by the Image provider [\#583](https://github.com/fzaninotto/Faker/pull/583) ([fzaninotto](https://github.com/fzaninotto))
* Fixed typos in Peruvian (es\_PE) Person provider [\#581](https://github.com/fzaninotto/Faker/pull/581) [\#580](https://github.com/fzaninotto/Faker/pull/580) ([ysramirez](https://github.com/ysramirez))
* Added instructions for installing with composer to readme.md [\#572](https://github.com/fzaninotto/Faker/pull/572) ([totophe](https://github.com/totophe))
* Added Kazakh (kk\_KZ) locale [\#569](https://github.com/fzaninotto/Faker/pull/569) ([YerlenZhubangaliyev](https://github.com/YerlenZhubangaliyev))
* Added Korean (ko\_KR) locale [\#566](https://github.com/fzaninotto/Faker/pull/566) ([pearlc](https://github.com/pearlc))
* Fixed file provider to ignore unreadable and special files [\#565](https://github.com/fzaninotto/Faker/pull/565) ([svrnm](https://github.com/svrnm))
* Fixed Dutch (nl\_NL) Address and Person providers [\#560](https://github.com/fzaninotto/Faker/pull/560) ([killerog](https://github.com/killerog))
* Fixed Dutch (nl\_NL) Person provider [\#559](https://github.com/fzaninotto/Faker/pull/559) ([pauledenburg](https://github.com/pauledenburg))
* Added Russian (ru\_RU) Bank names provider [\#553](https://github.com/fzaninotto/Faker/pull/553) ([wizardjedi](https://github.com/wizardjedi))
* Added mobile phone function in French (fr\_FR) provider [\#552](https://github.com/fzaninotto/Faker/pull/552) ([kletellier](https://github.com/kletellier))
* Added phpdoc for new magic methods in Generator to help IntelliSense completion [\#550](https://github.com/fzaninotto/Faker/pull/550) ([stof](https://github.com/stof))
* Fixed File provider bug 'The first argument to copy() function cannot be a directory' [\#547](https://github.com/fzaninotto/Faker/pull/547) ([svrnm](https://github.com/svrnm))
* Added new Brazilian (pt\_BR) Providers [\#545](https://github.com/fzaninotto/Faker/pull/545) ([igorsantos07](https://github.com/igorsantos07))
* Fixed ability to seed the generator [\#543](https://github.com/fzaninotto/Faker/pull/543) ([schmengler](https://github.com/schmengler))
* Added streetAddress formatter to Russian (ru\_RU) provider [\#542](https://github.com/fzaninotto/Faker/pull/542) ([ZAYEC77](https://github.com/ZAYEC77))
* Fixed Internet provider warning "Could not create transliterator"* [\#541](https://github.com/fzaninotto/Faker/pull/541) ([fonsecas72](https://github.com/fonsecas72))
* Fixed Spanish for Argentina (es\_AR) Address provider [\#540](https://github.com/fzaninotto/Faker/pull/540) ([ivanmirson](https://github.com/ivanmirson))
* Fixed region names in French for Belgium (fr\_BE) address provider [\#536](https://github.com/fzaninotto/Faker/pull/536) ([miclf](https://github.com/miclf))
* Fixed broken Doctrine2 link in README [\#534](https://github.com/fzaninotto/Faker/pull/534) ([JonathanKryza](https://github.com/JonathanKryza))
* Added link to faker-context Behat extension in readme [\#532](https://github.com/fzaninotto/Faker/pull/532) ([denheck](https://github.com/denheck))
* Added PHP 7.0 nightly to Travis build targets [\#525](https://github.com/fzaninotto/Faker/pull/525) ([TomasVotruba](https://github.com/TomasVotruba))
* Added Dutch (nl\_NL) color names [\#523](https://github.com/fzaninotto/Faker/pull/523) ([belendel](https://github.com/belendel))
* Fixed Chinese (zh\_CN) Address provider (remove Taipei) [\#522](https://github.com/fzaninotto/Faker/pull/522) ([asika32764](https://github.com/asika32764))
* Fixed phonenumber formats in Dutch (nl\_NL) PhoneNumber provider [\#521](https://github.com/fzaninotto/Faker/pull/521) ([SpaceK33z](https://github.com/SpaceK33z))
* Fixed Russian (ru\_RU) Address provider [\#518](https://github.com/fzaninotto/Faker/pull/518) ([glagola](https://github.com/glagola))
* Added Italian (it\_IT) Text provider [\#517](https://github.com/fzaninotto/Faker/pull/517) ([endelwar](https://github.com/endelwar))
* Added Norwegian (no\_NO) locale [\#515](https://github.com/fzaninotto/Faker/pull/515) ([phaza](https://github.com/phaza))
* Added VAT number to Bulgarian (bg\_BG) Payment provider [\#512](https://github.com/fzaninotto/Faker/pull/512) ([ronanguilloux](https://github.com/ronanguilloux))
* Fixed UserAgent provider outdated user agents [\#511](https://github.com/fzaninotto/Faker/pull/511) ([ajbdev](https://github.com/ajbdev))
* Fixed `image()` formatter to make it work with temp dir of any (decent) OS [\#507](https://github.com/fzaninotto/Faker/pull/507) ([ronanguilloux](https://github.com/ronanguilloux))
* Added Persian (fa\_IR) locale [\#500](https://github.com/fzaninotto/Faker/pull/500) ([zoli](https://github.com/zoli))
* Added Currency Code formatter [\#497](https://github.com/fzaninotto/Faker/pull/497) ([stelgenhof](https://github.com/stelgenhof))
* Added VAT number to Belgium (be_BE) Payment provider [\#495](https://github.com/fzaninotto/Faker/pull/495) ([ronanguilloux](https://github.com/ronanguilloux))
* Fixed `imageUrl` formatter bug where it would always return the same image [\#494](https://github.com/fzaninotto/Faker/pull/494) ([fzaninotto](https://github.com/fzaninotto))
* Added more Indonesian (id\_ID) providers [\#493](https://github.com/fzaninotto/Faker/pull/493) ([deerawan](https://github.com/deerawan))
* Added Indonesian (id\_ID) locale [\#492](https://github.com/fzaninotto/Faker/pull/492) ([stoutZero](https://github.com/stoutZero))
* Fixed unique generator performance [\#491](https://github.com/fzaninotto/Faker/pull/491) ([ikwattro](https://github.com/ikwattro))
* Added transliterator to `email` and `username` [\#490](https://github.com/fzaninotto/Faker/pull/490) ([fzaninotto](https://github.com/fzaninotto))
* Added Hungarian (hu\_HU) Text provider [\#486](https://github.com/fzaninotto/Faker/pull/486) ([lintaba](https://github.com/lintaba))
* Fixed CakePHP Entity Popolator (some cases where no entities prev. inserted) [\#483](https://github.com/fzaninotto/Faker/pull/483) ([jadb](https://github.com/jadb))
* Added Color and DateTime Turkish (tr\_TR) Providers [\#481](https://github.com/fzaninotto/Faker/pull/481) ([behramcelen](https://github.com/behramcelen))
* Added Latvian (lv\_LV) `personalIdentityNumber` formatter [\#472](https://github.com/fzaninotto/Faker/pull/472) ([MatissJanis](https://github.com/MatissJanis))
* Added VAT number to Austrian (at_AT) Payment provider [\#470](https://github.com/fzaninotto/Faker/pull/470) ([ronanguilloux](https://github.com/ronanguilloux))
* Fixed missing @return phpDoc in Payment provider [\#469](https://github.com/fzaninotto/Faker/pull/469) ([ronanguilloux](https://github.com/ronanguilloux))
* Added SWIFT/BIC payment type formatter to the Payment provider [\#465](https://github.com/fzaninotto/Faker/pull/465) ([ronanguilloux](https://github.com/ronanguilloux))
* Fixed small typo in Base provider exception [\#460](https://github.com/fzaninotto/Faker/pull/460) ([miclf](https://github.com/miclf))
* Added Georgian (ka\_Ge) locale [\#457](https://github.com/fzaninotto/Faker/pull/457) ([lperto](https://github.com/lperto))
* Added PSR-4 Autoloading [\#455](https://github.com/fzaninotto/Faker/pull/455) ([GrahamCampbell](https://github.com/GrahamCampbell))
* Added Uganda (en_UG) locale [\#454](https://github.com/fzaninotto/Faker/pull/454) ([tharoldD](https://github.com/tharoldD))
* Added `regexify` formatter, generating a random string based on a regular expression [\#453](https://github.com/fzaninotto/Faker/pull/453) ([fzaninotto](https://github.com/fzaninotto))
* Added shuffle formatter, to shuffle an array or a string [\#452](https://github.com/fzaninotto/Faker/pull/452) ([fzaninotto](https://github.com/fzaninotto))
* Added ISBN-10 & ISBN-13 codes formatters to Barcode provider [\#451](https://github.com/fzaninotto/Faker/pull/451) ([gietos](https://github.com/gietos))
* Fixed Russian (ru\_RU) middle names (different for different genders) [\#450](https://github.com/fzaninotto/Faker/pull/450) ([gietos](https://github.com/gietos))
* Fixed Ukranian (uk\_UA) Person provider [\#448](https://github.com/fzaninotto/Faker/pull/448) ([aivus](https://github.com/aivus))
* Added Vietnamese (vi\_VN) locale [\#447](https://github.com/fzaninotto/Faker/pull/447) ([huy95](https://github.com/huy95))
* Added type hint to the Documentor constructor [\#446](https://github.com/fzaninotto/Faker/pull/446) ([JeroenDeDauw](https://github.com/JeroenDeDauw))
* Fixed Russian (ru\_RU) Person provider (joined names) [\#445](https://github.com/fzaninotto/Faker/pull/445) ([aivus](https://github.com/aivus))
* Added English (en\_GB) `mobileNumber` methods [\#438](https://github.com/fzaninotto/Faker/pull/438) ([daveblake](https://github.com/daveblake))
* Added Traditional Chinese (zh\_TW) Realtext provider [\#434](https://github.com/fzaninotto/Faker/pull/434) ([tzhuan](https://github.com/tzhuan))
* Fixed first name in Spanish for Argentina (es\_AR) Person provider [\#433](https://github.com/fzaninotto/Faker/pull/433) ([fzaninotto](https://github.com/fzaninotto))
* Fixed Canadian (en_CA) state abbreviation for Nunavut [\#430](https://github.com/fzaninotto/Faker/pull/430) ([julien-c](https://github.com/julien-c))
* Added CakePHP ORM entity populator [\#428](https://github.com/fzaninotto/Faker/pull/428) ([jadb](https://github.com/jadb))
* Added Traditional Chinese (zh\_TW) locale [\#427](https://github.com/fzaninotto/Faker/pull/427) ([tzhuan](https://github.com/tzhuan))
* Fixed typo in Doctrine Populator phpDoc [\#425](https://github.com/fzaninotto/Faker/pull/425) ([ihsanudin](https://github.com/ihsanudin))
* Added Chinese (zh_CN) Internet provider [\#424](https://github.com/fzaninotto/Faker/pull/424) ([Lisso-Me](https://github.com/Lisso-Me))
* Added Country ISO 3166-1 alpha-3 code to the Miscellaneous provider[\#422](https://github.com/fzaninotto/Faker/pull/422) ([gido](https://github.com/gido))
* Added English (en\_GB) Person provider [\#421](https://github.com/fzaninotto/Faker/pull/421) ([AlexCutts](https://github.com/AlexCutts))
* Added missing tests for the Color Provider [\#420](https://github.com/fzaninotto/Faker/pull/420) ([bessl](https://github.com/bessl))
* Added Nepali (ne\_NP) locale [\#419](https://github.com/fzaninotto/Faker/pull/419) ([ankitpokhrel](https://github.com/ankitpokhrel))
* Fixed latitude and longitude formatters bug (numeric value out of range for 32bits) [\#416](https://github.com/fzaninotto/Faker/pull/416) ([fzaninotto](https://github.com/fzaninotto))
* Added a dedicated calculator Luhn calculator service [\#414](https://github.com/fzaninotto/Faker/pull/414) ([fzaninotto](https://github.com/fzaninotto))
* Fixed Russian (ru_RU) Person provider (removed lowercase duplications) [\#413](https://github.com/fzaninotto/Faker/pull/413) ([Ragazzo](https://github.com/Ragazzo))
* Fixed barcode formatter (improved speed, added tests) [\#412](https://github.com/fzaninotto/Faker/pull/412) ([fzaninotto](https://github.com/fzaninotto))
* Added ipv4 and barcode formatters tests [\#410](https://github.com/fzaninotto/Faker/pull/410) ([bessl](https://github.com/bessl))
* Fixed typos in various comments blocks [\#409](https://github.com/fzaninotto/Faker/pull/409) ([bessl](https://github.com/bessl))
* Fixed InternetTest (replaced regex with PHP filter) [\#406](https://github.com/fzaninotto/Faker/pull/406) ([bessl](https://github.com/bessl))
* Added password formatter to the Internet provider[\#402](https://github.com/fzaninotto/Faker/pull/402) ([fzaninotto](https://github.com/fzaninotto))
* Added Company and Internet Austrian (de\_AT) Providers [\#400](https://github.com/fzaninotto/Faker/pull/400) ([bessl](https://github.com/bessl))
* Added third-party libraries section in README [\#399](https://github.com/fzaninotto/Faker/pull/399) ([fzaninotto](https://github.com/fzaninotto))
* Added Spanish for Venezuela (es\_VE) locale [\#398](https://github.com/fzaninotto/Faker/pull/398) ([DIOHz0r](https://github.com/DIOHz0r))
* Added PhoneNumber Autrian (de\_AT) Provider, and missing test for the 'locale' method. [\#395](https://github.com/fzaninotto/Faker/pull/395) ([bessl](https://github.com/bessl))
* Removed wrongly localized Lorem provider [\#394](https://github.com/fzaninotto/Faker/pull/394) ([fzaninotto](https://github.com/fzaninotto))
* Fixed Miscellaneous provider (made the `locale` formatter static) [\#390](https://github.com/fzaninotto/Faker/pull/390) ([bessl](https://github.com/bessl))
* Added a unit test file for the Miscellaneous Provider [\#389](https://github.com/fzaninotto/Faker/pull/389) ([bessl](https://github.com/bessl))
* Added warning in README about using `rand()`` and the seed functions [\#386](https://github.com/fzaninotto/Faker/pull/386) ([paulvalla](https://github.com/paulvalla))
* Fixed French (fr\_FR) Person provider (Uppercased a first name) [\#385](https://github.com/fzaninotto/Faker/pull/385) ([netcarver](https://github.com/netcarver))
* Added Russian (ru\_RU) and Ukrainian (uk\_UA) Text providers [\#383](https://github.com/fzaninotto/Faker/pull/383) ([terion-name](https://github.com/terion-name))
* Added more street prefixes to French (fr\_FR) Address provider [\#381](https://github.com/fzaninotto/Faker/pull/381) ([ronanguilloux](https://github.com/ronanguilloux))
* Added PHP 5.6 to CI targets [\#378](https://github.com/fzaninotto/Faker/pull/378) ([GrahamCampbell](https://github.com/GrahamCampbell))
* Fixed spaces remaining at the end of liine in various files [\#377](https://github.com/fzaninotto/Faker/pull/377) ([GrahamCampbell](https://github.com/GrahamCampbell))
* Fixed UserAgent provider (added space before processor on linux platform) [\#374](https://github.com/fzaninotto/Faker/pull/374) ([TomK](https://github.com/TomK))
* Added Company generator for Russian (ru\_RU) locale [\#371](https://github.com/fzaninotto/Faker/pull/371) ([kix](https://github.com/kix))
* Fixed Russian (ru\_RU) Color provider (uppercase letters) [\#370](https://github.com/fzaninotto/Faker/pull/370) ([semanser](https://github.com/semanser))
* Added more Polish (pl\_PL) phone numbers [\#369](https://github.com/fzaninotto/Faker/pull/369) ([piotrantosik](https://github.com/piotrantosik))
* Fixed Ruby Faker link in readme [\#368](https://github.com/fzaninotto/Faker/pull/368) ([philsturgeon](https://github.com/philsturgeon))
* Added more Japanese (ja\_JP) names in Person provider [\#366](https://github.com/fzaninotto/Faker/pull/366) ([kumamidori](https://github.com/kumamidori))
* Added Slovenian (sl\_SL) locale [\#363](https://github.com/fzaninotto/Faker/pull/363) ([alesf](https://github.com/alesf))
* Fixed German (de\_DE) Person provider (first names) [\#362](https://github.com/fzaninotto/Faker/pull/362) ([mikehaertl](https://github.com/mikehaertl))
* Fixed Ukrainian (uk\_UA) Person providr (there is no such letter "ы" in  Ukrainian) [\#359](https://github.com/fzaninotto/Faker/pull/359) ([nazar-pc](https://github.com/nazar-pc))
* Fixed Chinese (zh\_CN) PhoneNumber provider (the length of mobile phone number is 11) [\#358](https://github.com/fzaninotto/Faker/pull/358) ([byan](https://github.com/byan))
* Added Arabic (ar_\JO) Locale [\#357](https://github.com/fzaninotto/Faker/pull/357) ([zrashwani](https://github.com/zrashwani))
* Fixed Czech (cs\_CZ) Person provider (missing lowercase in last name) [\#355](https://github.com/fzaninotto/Faker/pull/355) ([halaxa](https://github.com/halaxa))
* Fixed French for Belgium (fr\_BE) Address Provider (doubled city names) [\#354](https://github.com/fzaninotto/Faker/pull/354) ([miclf](https://github.com/miclf))
* Added Biased Integer Provider [\#332](https://github.com/fzaninotto/Faker/pull/332) ([TimWolla](https://github.com/TimWolla))
* Added Swedish (sv\_SE) locale [\#316](https://github.com/fzaninotto/Faker/pull/316) ([ulrikjohansson](https://github.com/ulrikjohansson))
* Added English for New Zealand (en\_NZ) locale [\#283](https://github.com/fzaninotto/Faker/pull/283) ([JasonMortonNZ](https://github.com/JasonMortonNZ))
* Added mention of external Provider for cron expressions to readme[\#498](https://github.com/fzaninotto/Faker/pull/498) ([swekaj](https://github.com/swekaj))

## 2014-06-04, v1.4.0

* Fixed typo in Slovak person names (cinan)
* Added tests for uk_UA providers (serge-kuharev)
* Fixed numerify() performance by making it 30% faster (fzaninotto)
* Added strict option to randomNumber to force number of digits (fzaninotto)
* Fixed randomNumber usage duplicating numberBetween (fzaninotto)
* Fixed address provider for latvian language (MatissJA)
* Added Czech Republic (cs_CZ) address, company, datetime and text providers (Mikulas)
* Fixed da_DK Person provider data containing an 'unnamed' person (tolnem)
* Added slug provider (fzaninotto)
* Fixed IDE insights for new local IP and MAC address providers (hugofonseca)
* Added firstname gender method to all Person providers (csanquer)
* Fixed tr_TR email service, city name, person, and phone number formats (ogunkarakus)
* Fixed US_en state list (fzaninotto)
* Fixed en_US address provider so state abbr are ISO 3166 codes (Garbee)
* Added local IP and MAC address providers (kielabokkie)
* Fixed typo in century list affecting the century provider (fzaninotto)
* Added default value to optional modifier (joshuajabbour)
* Fixed Portuguese phonenumbers have 9 digits (hugofonseca)
* Added fileCopy to File provider to simulate file upload (stefanosala)
* Added pt_PT providers (hugofonseca)
* Fixed dead code in text provider (hugofonseca)
* Fixed IDE insights for magic properties (hugofonseca)
* Added tin (NIF) generator for pt_PT provider (hugofonseca)
* Fixed numberBetween max default value handling (fzaninotto)
* Added pt_PT phone number provider (hugofonseca)
* Fixed PSR-2 standards and add make task to force it on Travis (terite)
* Added new ro_RO Personal Numerical Code (CNP) and phone number providers (avataru)
* Fixed Internet provider for sk_SK locale (cinan)
* Fixed typo in en_ZA Internet provider (bjorntheart)
* Fixed phpdoc for DateTime magic methods (stof)
* Added doc about seeding with maximum timestamp using dateTime formatters (fzaninotto)
* Added Maximum Timestamp option to get always same unix timestamp when using a fixed seed (csanquer)
* Added Montenegrian (me_ME) providers (ognjenm)
* Added ean barcode provider (nineinchnick)
* Added fullPath parameter to Image provider (stefanosala)
* Added more Polish company formats (nineinchnick)
* Added Polish realText provider (nineinchnick)
* Fixed remaining non-seedable random generators (terite)
* Added randomElements provider (terite)
* Added French realText provider (fzaninotto)
* Fixed realText provider bootstrap slowness (fzaninotto)
* Added realText provider for English and German, based on Markov Chains Generator (TimWolla)
* Fixed address format in nl_NL provider (doenietzomoeilijk)
* Fixed potentially offensive word from last name list (joshuajabbour)
* Fixed reamde documentation about the optional modifier (cryode)
* Fixed Image provider and documentor routine (fzaninotto)
* Fixed IDE insights for methods (PedroTroller)
* Fixed missing data in en_US Address provider (Garbee)
* Added Bengali (bn_BD) providers (masnun)
* Fixed warning on test file when short tags are on (bateller)
* Fixed Doctrine populator undefined index warning (dbojdo)
* Added French Canadian (fr_CA) Address and Person providers (marcaube)
* Fixed typo in NullGenerator (mhanson01)
* Fixed Doctrine populator issue with one-to-one nullable relationship (jpetitcolas)
* Added Canadian English (en_CA) address and phone number providers (cviebrock)
* Fixed duplicated Payment example in readme (Garbee)
* Fixed Polish (pl_PL) Person provider data (czogori)
* Added Hungarian (hu_HU) providers (sagikazarmark)
* Added 'kana' (ja_JP) name formatters (kzykhys)
* Added allow_failure for hhvm to travis-ci and test against php 5.5 (toin0u)

## 2013-12-16, v1.3.0

* Fixed state generator in Australian (en_AU) provider (sebklaus)
* Fixed IDE insights for locale specific providers (ulrikjohansson)
* Added English (South Africa) (en_ZA) person, address, Internet and phone number providers (dmfaux)
* Fixed integer values overflowing on signed INTEGER columns on Doctrine populator (Thinkscape)
* Fixed spelling error in French (fr_FR) address provider (leihog)
* Added improvements based on SensioLabsInsights analysis
* Fixed Italian (it_IT) email provider (garak)
* Added Spanish (es_ES) Internet provider (eusonlito)
* Added English Philippines (en_PH) address provider (kamote)
* Added Brazilian (pt_BR) email provider data (KennedyTedesco)
* Fixed UK country code (pgscandeias)
* Added Peruvian (es_PE) person, address, phone number, and company providers (cslucano)
* Added Ukrainian (uk_UA) color provider (ruden)
* Fixed Ukrainian (uk_UA) namespace and email translitteration (ruden)
* Added Romanian (Moldova) (ro_MD) person, address, and phone number providers (AlexanderC)
* Added IBAN generator for every currently known locale that uses it (nineinchnick)
* Added Image generation powered by LoremPixel (weotch)
* Fixed missing timezone with dateTimeBetween (baldurrensch)
* Fixed call to undefined method cardType in Payment (WMeldon)
* Added Romanian (ro_RO) address and person providers (calina-c)
* Fixed Doctrine populator to use ObjectManager instead of EntityManagerInterface (mgiustiniani)
* Fixed docblock for Provider\Base::unique() (pschultz)
* Added Payment providers (creditCardType, creditCardNumber, creditCardExpirationDate, creditCardExpirationDateString) (pomaxa)
* Added unique() modifier
* Added IDE insights to allow better intellisense/phpStorm autocompletion (thallisphp)
* Added Polish (pl_PL) address provider, personal identity number and pesel number generator (nineinchnick)
* Added Turkish (tr_TR) address provider, and improved internet provider (hasandz)
* Fixed Propel column number guesser to use signed range of values (gunnarlium)
* Added Greek (el_GR) person, address, and phone number providers (georgeharito)
* Added Austrian (en_AU) address, Internet, and phone number providers (rcuddy)
* Fixed phpDoc in Doctrine Entity populator (rogamoore)
* Added French (fr_FR) phone number formats (vchabot)
* Added optional() modifier (weotch)
* Fixed typo in the Person provider documentation (jtreminio)
* Fixed Russian (ru_RU) person format (alexshadow007)
* Added Japanese (ja_JP) person, address, Internet, phone number, and company providers (kumamidori)
* Added color providers, driver license and passport number formats for the ru_RU locale (pomaxa)
* Added Latvian (lv_LV) person, address, Internet, and phone number providers (pomaxa)
* Added Brazilian (pt_BR) Internet provider (vjnrv)
* Added more Czech (cs_CZ) lastnames (petrkle)
* Added Chinese Simplified (zh_CN) person, address, Internet, and phone number providers (tlikai)
* Fixed Typos (pborelli)
* Added Color provider with hexColor, rgbColor, rgbColorAsArray, rgbCssColor, safeColorName, and colorName formatters (lsv)
* Added support for associative arrays in `randomElement` (aRn0D)

## 2013-06-09, v1.2.0

* Added new provider for fr_BE locale (jflefebvre)
* Updated locale provider to use a static locale list (spawn-guy)
* Fixed invalid UTF-8 sequence in domain provider with the Bulgarian provider (Dynom)
* Fixed the nl_NL Person provider (Dynom)
* Removed all requires and added the autoload definition to composer (Dynom)
* Fixed encoding problems in nl_NL Address provider (Dynom)
* Added icelandic provider (is_IS) (birkir)
* Added en_CA address and phone numbers (cviebrock)
* Updated safeEmail provider to be really safe (TimWolla)
* Documented alternative randomNumber usage (Seldaek)
* Added basic file provider (anroots)
* Fixed use of fourth argument on Doctrine addEntity (ecentinela)
* Added nl_BE provider (wimvds)
* Added Random Float provider (csanquer)
* Fixed bug in Faker\ORM\Doctrine\Populator (mmf-amarcos)
* Updated ru_RU provider (rmrevin)
* Added safe email domain provider (csanquer)
* Fixed latitude provider (rumpl)
* Fixed unpredictability of fake data generated by Faker\Provider\Base::numberBetween() (goatherd)
* Added uuid provider (goatherd)
* Added possibility to call methods on Doctrine entities, possibility to generate unique id (nenadalm)
* Fixed prefixes typos in 'pl_PL' Person provider (krymen)
* Added more fake data to the Ukraininan providers (lysenkobv)
* Added more fake data to the Italian providers (EmanueleMinotto)
* Fixed spaces appearing in generated emails (alchy58)
* Added Armenian (hy_AM) provider (artash)
* Added Generation of valid SIREN & SIRET codes to French providers (alexsegura)
* Added Dutch (nl_NL) provider (WouterJ)
* Fixed missing typehint in Base::__construct() (benja-M-1)
* Fixed typo in README (benja-M-1)
* Added Ukrainian (ua_UA) provider (rsvasilyev)
* Added Turkish (tr_TR) Provider (faridmovsumov)
* Fixed executable bit in some PHP files (siwinski)
* Added Brazilian Portuguese (pt_BR) provider (oliveiraev)
* Added Spanish (es_ES) provider (ivannis)
* Fixed Doctrine populator to allow for the population of entity data that has associations to other entities (afishnamedsquish)
* Added Danish (da_DK) providers (toin0u)
* Cleaned up whitespaces (toin0u)
* Fixed utf-8 bug with lowercase generators (toin0u)
* Fixed composer.json (Seldaek)
* Fixed bug in Doctrine EntityPopulator (beberlei)
* Added Finnish (fi_FI) provider (drodil)

## 2012-10-29, v1.1.0

* Updated text provider to return paragraphs as a string instead of array. Great for populating markdown textarea fields (Seldaek)
* Updated dateTimeBetween to accept DateTime instances (Seldaek)
* Added random number generator between a and b, simply like rand() (Seldaek)
* Fixed spaces in generated emails (blaugueux)
* Fixed Person provider in Russian locale (Isamashii)
* Added new UserAgent provider (blaugueux)
* Added locale generator to Miscellaneous provider (blaugueux)
* Added timezone generator to DateTime provider (blaugueux)
* Added new generators to French Address providers (departments, regions) (geoffrey-brier)
* Added new generators to French Company provider (catch phrase, SIREN, and SIRET numbers) (geoffrey-brier)
* Added state generator to German Address provider (Powerhamster)
* Added Slovak provider (bazo)
* Added latitude and longitude formatters to Address provider (fixe)
* Added Serbian provider (umpirsky)

## 2012-07-10, v1.0.0

* Initial Version
