# This is a basic workflow to help you get started with Actions

name: Tests

# Controls when the action will run. 
on:
  push:
  pull_request:

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  run_tests:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest
    
    strategy:
      fail-fast: true
      matrix:
        php: [7.2, 7.3, 7.4, 8.0, 8.1]
   
    name: PHP ${{ matrix.php }}

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      - name: Checkout code
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite
          tools: composer:v2
          coverage: none

      - name: Install dependencies
        uses: nick-invision/retry@v1
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer update --prefer-dist --no-interaction --no-progress

      - name: Execute tests
        run: vendor/bin/phpunit --verbose
