<?php

namespace App\Transformers\Events;

use Illuminate\Http\Request;

class ContestEventsTransformer
{
    public static function transform($eventKey, Request $request, $response = null)
    {
        return [
            // 'match_id'    => $request->input('match_id')??'',
            // 'contest_id'  => $request->input('contest_id')??'',
            // 'entry_fee'   => $request->input('entry_fee')??'',
            'platform'    => $request->header('X-Platform') ?: $request->header('User-Agent'),
            'ip'          => $request->ip(),
        ];
    }
}
