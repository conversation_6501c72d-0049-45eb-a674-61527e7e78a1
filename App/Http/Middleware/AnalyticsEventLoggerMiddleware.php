<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Auth;
use App\Jobs\LogAnalyticsEventJob;
use App\Transformers\AnalyticsEventTransformer;
use App\Helpers\CleverTapHelper;
class AnalyticsEventLoggerMiddleware
{
    protected $eventData = [];
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $this->eventData = [
            'path' => ltrim($request->path(), '/'),
            'user_id' => Auth::id() ?? $request->input('user_id'),
            'customData' => [
                'ip' => $request->ip(),
                'device' => $request->header('User-Agent'),
            ]
        ];

        return $next($request);
    }

    public function terminate($request, $response)
    {
        try {
            $user_id = Auth::id() ?? $request->input('user_id');
            $statusCode = $response->getStatusCode();
            if ($user_id == '12501981' && $statusCode == 200) {

                $content = $response->getContent();
                $data = json_decode($content, true);
                \Log::info('AnalyticsEventLoggerMiddleware:', [
                    'status' => $statusCode,
                    'request' => $request->all(),

                ]);

                // $path = ltrim($request->path(), '/'); // e.g. api/auth/login
                // $path = preg_replace('/^api\//', '', $path); // remove "api/" prefix 

                $path = preg_replace('/^api\//', '', ltrim($request->path(), '/'));
                $eventConfigs = Config::get("analytics_events.$path");
                \Log::info('$event && $identity:', [
                    'path' => $path,
                    'eventConfigs' => $eventConfigs,
                ]);
                if ($eventConfigs) {
                    $config = $eventConfigs;
                    $event = $config['event'] ?? null;
                    $message = $config['message'] ?? null;
                    $identity = Auth::id() ?? $request->input('user_id');


                    if ($event && $identity) {
                        \Log::info('$event && $identity: sahi chal raha hai ', [
                            'event' => $event,
                            'identity' => $identity,
                        ]);
                        $customData = AnalyticsEventTransformer::getPayload($path, $request, $data);
                        \Log::info('AnalyticsEventTransformer customData', [
                            'customData' => $customData,
                        ]);
                        //  $response = CleverTapHelper::sendEvent($identity, $event, $customData);
                        dispatch(new LogAnalyticsEventJob($identity, $event, $message, $customData));
                    } else {
                        \Log::info('Event or Identity not match');
                    }
                } else {
                    \Log::info('eventConfigs confir error issue');
                }
            }
        } catch (\Throwable $e) {
            \Log::warning('CleverTap terminate middleware error: ' . $e->getMessage());
        }
    }
}
