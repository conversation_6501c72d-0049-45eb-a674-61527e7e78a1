<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Helpers\CleverTapHelper;

class LogAnalyticsEventJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 10;


    protected $identity;
    protected $event;
    protected $message;
    protected $customData;


    /**
     * Create a new job instance.
     */
    public function __construct($identity, $event, $message = null, $customData = [])
    {
        $this->identity = $identity;
        $this->event = $event;
        $this->message = $message;
        $this->customData = $customData;
    }


    public function handle(): void
{
    try {
        \Log::info('customData value:', ['customData' => $this->customData]);

        // Use the exact same call as the working direct call
        $response = CleverTapHelper::sendEvent($this->identity, $this->event, $this->customData);

        // ✅ Log response
        \Log::info("CleverTap API Response:", $response);
        \Log::info("LogAnalyticsEventJobCleverTap Event Logged: {$this->event} for user {$this->identity}");
    } catch (\Exception $e) {
        \Log::warning('LogAnalyticsEventJobCleverTap Event Job Failed: ' . $e->getMessage());
        throw $e;
    }
}


    public function tags(): array
    {
        return ['clevertap', 'event:' . $this->event];
    }

    public function failed(\Throwable $exception): void
    {
        \Log::error("CleverTap Job Failed after retries", [
            'identity' => $this->identity,
            'event' => $this->event,
            'error' => $exception->getMessage()
        ]);
    }
}
