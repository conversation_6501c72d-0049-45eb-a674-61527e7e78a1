<?php

namespace App\Helpers;

use App\Services\CurlService;
use Exception;
use Illuminate\Support\Facades\DB;

class CleverTapHelper
{

    /**
     * Send an event to CleverTap for the given user identity.
     */
    public static function sendEvent(string $identity, string $eventName, array $eventData = []): array
    {
        $payload = [
            'd' => [
                [
                    'objectId' => $identity,
                    'type'     => 'event',
                    'evtName'  => $eventName,
                    'evtData'  => $eventData,
                ],
            ],
        ];

        return self::sendRequestToCleverTap('upload', $payload);
    }

    /**
     * Create or update a CleverTap user profile.
     */
    public static function createOrUpdateProfile(string $identity, array $profileData): array
    {
        $payload = [
            'd' => [
                [
                    'objectId'    => $identity,
                    'type'        => 'profile',
                    'profileData' => $profileData,
                ],
            ],
        ];

        return self::sendRequestToCleverTap('upload', $payload);
    }

    /**
     * Add or update device push token to the user's profile.
     */
    public static function addDeviceToken(string $identity, string $token): array
    {
        $payload = [
            'd' => [
                [
                    'objectId'    => $identity,
                    'type'        => 'profile',
                    'profileData' => [
                        'PushToken' => $token,
                    ],
                ],
            ],
        ];

        return self::sendRequestToCleverTap('upload', $payload);
    }

    public static function mergeIdentities(string $fromIdentity, string $toIdentity): array
    {
        $payload = [
            'from' => $fromIdentity,
            'to'   => $toIdentity,
        ];

        return self::sendRequestToCleverTap('merge', $payload);
    }

    /**
     * Alias method for sendEvent - for backward compatibility
     */
    public static function sendEventToCleverTap(string $identity, string $eventName, array $eventData = []): array
    {
        return self::sendEvent($identity, $eventName, $eventData);
    }

    /**
     * Send the actual request to CleverTap via CurlService.
     */
    protected static function sendRequestToCleverTap(string $endpoint, array $payload): array
    {
        try {
            $cleverTapSettings = DB::table("settings")->where("setting_key", "CLEVERTAP_DEV_KEY")->value('value');
            if (empty($cleverTapSettings)) {
                throw new Exception('CleverTap configuration not found in settings table.');
            }

            $settingValues = json_decode($cleverTapSettings, true);

            $accountId = $settingValues['project_id'] ?? ''; //CLEVERTAP_ACCOUNT_ID=your_project_id
            $passcode = $settingValues['passcode'] ?? '';  // CLEVERTAP_PASSCODE=your_passcode
            $region = $settingValues['region'] ?? 'in1';  // CLEVERTAP_REGION=in1

            if (!$accountId || !$passcode || !$region) {
                throw new Exception('Missing CleverTap credentials.');
            }

            $url = "https://" . $region . ".api.clevertap.com/1/" . $endpoint;

            $headers = [
                'X-CleverTap-Account-Id: ' . $accountId,
                'X-CleverTap-Passcode: ' . $passcode,
                'Content-Type: application/json',
            ];

            \Log::info('CleverTap API Request', [
                'url' => $url,
                'payload' => $payload,
                'headers' => $headers,
            ]);

            $response = CurlService::makePostRequest($url, $payload, $headers);

            \Log::info('CleverTap API Response', [
                'http_code' => $response['http_code'] ?? 'unknown',
                'response_body' => $response['response'] ?? 'empty',
            ]);

            return [
                'success' => true,
                'status' => $response['http_code'] ?? 200,
                'response' => $response,
            ];
        } catch (Exception $e) {
            \Log::error('CleverTap API Error', [
                'message' => $e->getMessage(),
                'payload' => $payload ?? [],
                'endpoint' => $endpoint ?? '',
            ]);
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
