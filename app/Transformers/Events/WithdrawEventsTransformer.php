<?php

namespace App\Transformers\Events;

use Illuminate\Http\Request;

class WithdrawEventsTransformer
{
    public static function transform($eventKey, Request $request, $response = null)
    {
        return [
            'amount'      => $request->input('amount'),
            'method'      => $request->input('method'),
            'wallet_type' => $request->input('wallet_type'),
            'ip'          => $request->ip(),
        ];
    }
}
