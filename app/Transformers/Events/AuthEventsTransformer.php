<?php

namespace App\Transformers\Events;

use Illuminate\Http\Request;

class AuthEventsTransformer
{
    public static function transform($eventKey, Request $request, $response = null)
    {
        if ($eventKey === 'auth/send-mobile-otp') {
            return [
                'mobile'   => $request->input('mobile'),
                'otp_type' => $request->input('type'),
                'source'   => $request->input('source'),
                'ip'       => $request->ip(),
            ];
        }

        if ($eventKey === 'auth/verify-otp-login') {
            return [
                'mobile'   => $request->input('mobile'),
                'otp'      => $request->input('otp'),
                'success'  => true,
                'ip'       => $request->ip(),
            ];
        }

        if ($eventKey === 'auth/register') {
            return [
                'ref_code'    => $request->input('refer_code'),
                'device_type' => $request->header('X-Device-Type') ?: 'unknown',
                'ip'          => $request->ip(),
            ];
        }

        return [];
    }
}
