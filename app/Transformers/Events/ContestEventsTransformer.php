<?php

namespace App\Transformers\Events;

use Illuminate\Http\Request;

class ContestEventsTransformer
{
    public static function transform($eventKey, Request $request, $response = null)
    {
        return [
            'user_id'            => '12501981',
            'tour_id'            => 3478,
            'fantasy_type_id'    => 6,
            'fantasy_type'       => 'Ten One Fantasy',
            'category_id'        => 17,
            'category_name'      => 'Mini Grand League',
            'category_sub_text'  => 'Less competition for easy winnings',
            'sport_type'         => 1,
            'sport_key'          => 'CRICKET',
            'match_key'          => '91006',
            'join_id'            => 1393674209,
            'challenge_type'     => 'money',
            'message'            => 'Contest Joined',
            'platform'           => $request->header('X-Platform') ?: $request->header('User-Agent'),
            'ip'                 => $request->ip(),
        ];
    }
}
