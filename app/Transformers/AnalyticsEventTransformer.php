<?php

namespace App\Transformers;

use Illuminate\Http\Request;
use App\Transformers\Events\AuthEventsTransformer;
use App\Transformers\Events\ContestEventsTransformer;
use App\Transformers\Events\WithdrawEventsTransformer;
use App\Transformers\Events\DefaultEventTransformer;
use App\Transformers\Events\MatchEventsTransformer;

class AnalyticsEventTransformer
{
    public static function getPayload($eventKey, Request $request, $response = null)
    {
        if (strpos($eventKey, 'auth/joinleague') === 0) {
            return ContestEventsTransformer::transform($eventKey, $request, $response);
        }

        //  if ($eventKey === 'auth/getplayerlist') {
        //     return ContestEventsTransformer::transform($eventKey, $request, $response);
        // }

        // if (in_array($eventKey, [
        //     'auth/send-mobile-otp',
        //     'auth/verify-otp-login',
        //     'auth/register'
        // ])) {
        //     return AuthEventsTransformer::transform($eventKey, $request, $response);
        // }

        // if ($eventKey === 'auth/request-withdraw') {
        //     return WithdrawEventsTransformer::transform($eventKey, $request, $response);
        // }

        
        // if ($eventKey === 'auth/getmatchlist') {
        //     return MatchEventsTransformer::transform($eventKey, $request, $response);
        // }

        // Default fallback
        return DefaultEventTransformer::transform($eventKey, $request, $response);
    } 
}
