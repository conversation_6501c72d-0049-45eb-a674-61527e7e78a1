<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserController extends Controller
{
    /**
     * Get user details by ID
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserDetails(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|string|max:50'
        ]);

        $userId = $request->input('user_id');

        // Simulate user data (replace with actual database query)
        $userData = $this->generateUserData($userId);

        return response()->json([
            'success' => true,
            'message' => 'User details retrieved successfully',
            'data' => $userData,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Get multiple users details
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getMultipleUsers(Request $request): JsonResponse
    {
        $request->validate([
            'user_ids' => 'required|array|max:10',
            'user_ids.*' => 'required|string|max:50'
        ]);

        $userIds = $request->input('user_ids');
        $users = [];

        foreach ($userIds as $userId) {
            $users[] = $this->generateUserData($userId);
        }

        return response()->json([
            'success' => true,
            'message' => 'Multiple users details retrieved successfully',
            'data' => $users,
            'count' => count($users),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Get user profile with extended details
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserProfile(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|string|max:50',
            'include_stats' => 'boolean',
            'include_preferences' => 'boolean'
        ]);

        $userId = $request->input('user_id');
        $includeStats = $request->input('include_stats', false);
        $includePreferences = $request->input('include_preferences', false);

        $userData = $this->generateUserData($userId);

        // Add optional data based on request
        if ($includeStats) {
            $userData['stats'] = $this->generateUserStats($userId);
        }

        if ($includePreferences) {
            $userData['preferences'] = $this->generateUserPreferences($userId);
        }

        return response()->json([
            'success' => true,
            'message' => 'User profile retrieved successfully',
            'data' => $userData,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Search users by criteria
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function searchUsers(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2|max:100',
            'limit' => 'integer|min:1|max:50'
        ]);

        $query = $request->input('query');
        $limit = $request->input('limit', 10);

        // Simulate search results
        $users = [];
        for ($i = 1; $i <= $limit; $i++) {
            $users[] = $this->generateUserData("search_user_{$i}");
        }

        return response()->json([
            'success' => true,
            'message' => 'Users search completed successfully',
            'data' => $users,
            'query' => $query,
            'count' => count($users),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Generate mock user data
     *
     * @param string $userId
     * @return array
     */
    private function generateUserData(string $userId): array
    {
        $names = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown'];
        $teams = ['Team Alpha', 'Team Beta', 'Team Gamma', 'Team Delta', 'Team Omega'];
        $roles = ['Player', 'Captain', 'Coach', 'Manager', 'Analyst'];
        
        return [
            'user_id' => $userId,
            'name' => $names[array_rand($names)],
            'email' => strtolower(str_replace(' ', '.', $names[array_rand($names)])) . '@example.com',
            'team' => $teams[array_rand($teams)],
            'role' => $roles[array_rand($roles)],
            'status' => 'active',
            'level' => rand(1, 100),
            'experience_points' => rand(100, 10000),
            'join_date' => now()->subDays(rand(1, 365))->toDateString(),
            'last_active' => now()->subHours(rand(1, 24))->toISOString(),
            'avatar_url' => "https://api.dicebear.com/7.x/avataaars/svg?seed={$userId}",
            'is_online' => (bool) rand(0, 1),
            'country' => 'IN',
            'timezone' => 'Asia/Kolkata'
        ];
    }

    /**
     * Generate mock user stats
     *
     * @param string $userId
     * @return array
     */
    private function generateUserStats(string $userId): array
    {
        return [
            'games_played' => rand(10, 500),
            'games_won' => rand(5, 250),
            'win_rate' => round(rand(30, 90), 2),
            'total_score' => rand(1000, 50000),
            'average_score' => rand(100, 1000),
            'best_score' => rand(500, 2000),
            'achievements' => rand(1, 20),
            'rank' => rand(1, 1000),
            'streak' => rand(0, 15)
        ];
    }

    /**
     * Generate mock user preferences
     *
     * @param string $userId
     * @return array
     */
    private function generateUserPreferences(string $userId): array
    {
        return [
            'notifications' => [
                'email' => (bool) rand(0, 1),
                'push' => (bool) rand(0, 1),
                'sms' => (bool) rand(0, 1)
            ],
            'privacy' => [
                'profile_visible' => (bool) rand(0, 1),
                'stats_visible' => (bool) rand(0, 1),
                'online_status' => (bool) rand(0, 1)
            ],
            'game_settings' => [
                'difficulty' => ['easy', 'medium', 'hard'][rand(0, 2)],
                'sound_enabled' => (bool) rand(0, 1),
                'auto_save' => (bool) rand(0, 1)
            ],
            'language' => 'en',
            'theme' => ['light', 'dark'][rand(0, 1)]
        ];
    }
}
