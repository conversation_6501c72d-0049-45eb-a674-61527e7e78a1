<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Jobs\LogAnalyticsEventJob;
use Illuminate\Support\Facades\DB;

class CleverTapTestController extends Controller
{
    /**
     * Test CleverTap integration with queue
     */
    public function testCleverTapQueue(Request $request)
    {
        try {
            // Get user identity from request or use default
            $identity = $request->input('user_id', '12501981');
            $event = $request->input('event', 'test_event');
            $message = $request->input('message', 'Test Event from API');

            // Custom data for the event
            $customData = [
                'platform' => $request->header('User-Agent', 'API-Test'),
                'ip' => $request->ip(),
                'test_data' => $request->input('test_data', 'Sample test data'),
                'timestamp' => now()->toIso8601String(),
                'api_endpoint' => 'test-clevertap-queue'
            ];

            // Log the event in database before dispatching
            $eventRecord = DB::table('clevertap_events')->insertGetId([
                'user_identity' => $identity,
                'event_name' => $event,
                'event_data' => json_encode($customData),
                'message' => $message,
                'status' => 'pending',
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Dispatch the job to Redis queue
            dispatch(new LogAnalyticsEventJob($identity, $event, $message, $customData));

            // Update the record with job status
            DB::table('clevertap_events')
                ->where('id', $eventRecord)
                ->update(['job_id' => 'dispatched_' . time()]);

            return response()->json([
                'success' => true,
                'message' => 'CleverTap event dispatched to queue successfully',
                'data' => [
                    'event_id' => $eventRecord,
                    'user_identity' => $identity,
                    'event_name' => $event,
                    'custom_data' => $customData,
                    'queue_status' => 'dispatched'
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to dispatch CleverTap event',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get CleverTap events history
     */
    public function getEventsHistory(Request $request)
    {
        try {
            $limit = $request->input('limit', 20);
            $status = $request->input('status');
            $user_identity = $request->input('user_identity');

            $query = DB::table('clevertap_events')
                ->orderBy('created_at', 'desc');

            if ($status) {
                $query->where('status', $status);
            }

            if ($user_identity) {
                $query->where('user_identity', $user_identity);
            }

            $events = $query->limit($limit)->get();

            return response()->json([
                'success' => true,
                'data' => $events,
                'total' => $events->count()
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch events history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update CleverTap settings
     */
    public function updateSettings(Request $request)
    {
        try {
            $projectId = $request->input('project_id');
            $passcode = $request->input('passcode');
            $region = $request->input('region', 'in1');

            if (!$projectId || !$passcode) {
                return response()->json([
                    'success' => false,
                    'message' => 'project_id and passcode are required'
                ], 400);
            }

            $settings = [
                'project_id' => $projectId,
                'passcode' => $passcode,
                'region' => $region
            ];

            DB::table('settings')
                ->where('setting_key', 'CLEVERTAP_DEV_KEY')
                ->update([
                    'value' => json_encode($settings),
                    'updated_at' => now()
                ]);

            return response()->json([
                'success' => true,
                'message' => 'CleverTap settings updated successfully',
                'data' => $settings
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
