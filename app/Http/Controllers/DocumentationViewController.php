<?php

namespace App\Http\Controllers;

use App\Models\DocCategory;
use App\Models\DocSection;
use App\Models\DocContent;
use App\Models\DocTemplate;
use App\Models\DocSetting;
use Illuminate\Http\Request;

class DocumentationViewController extends Controller
{
    public function index()
    {
        $categories = DocCategory::active()
                                ->with(['activeSections.activeContents'])
                                ->ordered()
                                ->get();

        $template = DocTemplate::where('is_default', true)->first();
        $settings = $this->getDocumentationSettings();

        return view('documentation.index', compact('categories', 'template', 'settings'));
    }

    public function section($categorySlug, $sectionSlug)
    {
        $category = DocCategory::where('slug', $categorySlug)->active()->firstOrFail();
        $section = DocSection::where('slug', $sectionSlug)
                            ->where('category_id', $category->id)
                            ->active()
                            ->with('activeContents')
                            ->firstOrFail();

        $allCategories = DocCategory::active()
                                  ->with(['activeSections'])
                                  ->ordered()
                                  ->get();

        $template = DocTemplate::where('is_default', true)->first();
        $settings = $this->getDocumentationSettings();

        return view('documentation.section', compact('category', 'section', 'allCategories', 'template', 'settings'));
    }

    public function export($format = 'html')
    {
        $categories = DocCategory::active()
                                ->with(['activeSections.activeContents'])
                                ->ordered()
                                ->get();

        $template = DocTemplate::where('is_default', true)->first();
        $settings = $this->getDocumentationSettings();

        switch ($format) {
            case 'html':
                return $this->exportHtml($categories, $template, $settings);
            case 'pdf':
                return $this->exportPdf($categories, $template, $settings);
            case 'markdown':
                return $this->exportMarkdown($categories, $settings);
            default:
                abort(404);
        }
    }

    private function exportHtml($categories, $template, $settings)
    {
        $html = view('documentation.export.html', compact('categories', 'template', 'settings'))->render();
        
        $filename = 'documentation-' . date('Y-m-d-H-i-s') . '.html';
        
        return response($html)
                ->header('Content-Type', 'text/html')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    private function exportPdf($categories, $template, $settings)
    {
        // You can use libraries like DomPDF or wkhtmltopdf
        $html = view('documentation.export.pdf', compact('categories', 'template', 'settings'))->render();
        
        // Example with DomPDF (you need to install it)
        // $pdf = PDF::loadHTML($html);
        // return $pdf->download('documentation-' . date('Y-m-d-H-i-s') . '.pdf');
        
        return response($html)->header('Content-Type', 'text/html');
    }

    private function exportMarkdown($categories, $settings)
    {
        $markdown = view('documentation.export.markdown', compact('categories', 'settings'))->render();
        
        $filename = 'documentation-' . date('Y-m-d-H-i-s') . '.md';
        
        return response($markdown)
                ->header('Content-Type', 'text/markdown')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    public function search(Request $request)
    {
        $query = $request->get('q');
        
        if (!$query) {
            return redirect()->route('documentation.index');
        }

        $contents = DocContent::active()
                             ->with(['section.category'])
                             ->where(function($q) use ($query) {
                                 $q->where('title', 'LIKE', "%{$query}%")
                                   ->orWhere('content', 'LIKE', "%{$query}%");
                             })
                             ->orderBy('sort_order')
                             ->paginate(20);

        $categories = DocCategory::active()
                                ->with(['activeSections'])
                                ->ordered()
                                ->get();

        $template = DocTemplate::where('is_default', true)->first();
        $settings = $this->getDocumentationSettings();

        return view('documentation.search', compact('contents', 'categories', 'template', 'settings', 'query'));
    }

    public function api()
    {
        $categories = DocCategory::active()
                                ->with(['activeSections.activeContents'])
                                ->ordered()
                                ->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
            'meta' => [
                'total_categories' => $categories->count(),
                'total_sections' => $categories->sum(function($cat) {
                    return $cat->activeSections->count();
                }),
                'total_contents' => $categories->sum(function($cat) {
                    return $cat->activeSections->sum(function($section) {
                        return $section->activeContents->count();
                    });
                }),
                'generated_at' => now()->toISOString()
            ]
        ]);
    }

    private function getDocumentationSettings()
    {
        // You can create DocSetting model for this
        return [
            'title' => 'Laravel Documentation',
            'subtitle' => 'Complete Setup Guide',
            'version' => '1.0.0',
            'author' => 'Development Team',
            'last_updated' => now()->format('F j, Y'),
            'theme' => 'default'
        ];
    }

    public function bulkImport(Request $request)
    {
        $request->validate([
            'import_file' => 'required|file|mimes:json',
        ]);

        $file = $request->file('import_file');
        $data = json_decode(file_get_contents($file->path()), true);

        if (!$data) {
            return redirect()->back()->with('error', 'Invalid JSON file!');
        }

        try {
            \DB::beginTransaction();

            foreach ($data['categories'] as $categoryData) {
                $category = DocCategory::create([
                    'name' => $categoryData['name'],
                    'icon' => $categoryData['icon'] ?? null,
                    'description' => $categoryData['description'] ?? null,
                    'sort_order' => $categoryData['sort_order'] ?? 0,
                ]);

                foreach ($categoryData['sections'] as $sectionData) {
                    $section = DocSection::create([
                        'category_id' => $category->id,
                        'title' => $sectionData['title'],
                        'icon' => $sectionData['icon'] ?? null,
                        'description' => $sectionData['description'] ?? null,
                        'sort_order' => $sectionData['sort_order'] ?? 0,
                    ]);

                    foreach ($sectionData['contents'] as $contentData) {
                        DocContent::create([
                            'section_id' => $section->id,
                            'title' => $contentData['title'],
                            'content_type' => $contentData['content_type'],
                            'content' => $contentData['content'],
                            'language' => $contentData['language'] ?? null,
                            'metadata' => $contentData['metadata'] ?? null,
                            'sort_order' => $contentData['sort_order'] ?? 0,
                        ]);
                    }
                }
            }

            \DB::commit();

            return redirect()->route('admin.documentation.dashboard')
                           ->with('success', 'Documentation imported successfully!');

        } catch (\Exception $e) {
            \DB::rollback();
            return redirect()->back()->with('error', 'Import failed: ' . $e->getMessage());
        }
    }
}
