<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DocCategory;
use App\Models\DocSection;
use App\Models\DocContent;
use App\Models\DocTemplate;
use Illuminate\Http\Request;

class DocumentationController extends Controller
{
    // Dashboard
    public function dashboard()
    {
        $stats = [
            'categories' => DocCategory::count(),
            'sections' => DocSection::count(),
            'contents' => DocContent::count(),
            'active_categories' => DocCategory::active()->count(),
        ];

        $recentContents = DocContent::with(['section.category'])
                                   ->orderBy('updated_at', 'desc')
                                   ->limit(10)
                                   ->get();

        return view('admin.documentation.dashboard', compact('stats', 'recentContents'));
    }

    // Categories CRUD
    public function categories()
    {
        $categories = DocCategory::with('sections')->orderBy('sort_order')->get();
        return view('admin.documentation.categories.index', compact('categories'));
    }

    public function createCategory()
    {
        return view('admin.documentation.categories.create');
    }

    public function storeCategory(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'icon' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'integer|min:0',
        ]);

        DocCategory::create($request->all());

        return redirect()->route('admin.documentation.categories.index')
                        ->with('success', 'Category created successfully!');
    }

    public function editCategory(DocCategory $category)
    {
        return view('admin.documentation.categories.edit', compact('category'));
    }

    public function updateCategory(Request $request, DocCategory $category)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'icon' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'integer|min:0',
        ]);

        $category->update($request->all());

        return redirect()->route('admin.documentation.categories')
                        ->with('success', 'Category updated successfully!');
    }

    public function deleteCategory(DocCategory $category)
    {
        $category->delete();
        return redirect()->route('admin.documentation.categories')
                        ->with('success', 'Category deleted successfully!');
    }

    // Sections CRUD
    public function sections()
    {
        $sections = DocSection::with(['category', 'contents'])->orderBy('sort_order')->get();
        return view('admin.documentation.sections.index', compact('sections'));
    }

    public function createSection()
    {
        $categories = DocCategory::active()->orderBy('sort_order')->get();
        return view('admin.documentation.sections.create', compact('categories'));
    }

    public function storeSection(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:doc_categories,id',
            'title' => 'required|string|max:255',
            'icon' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'integer|min:0',
        ]);

        DocSection::create($request->all());

        return redirect()->route('admin.documentation.sections')
                        ->with('success', 'Section created successfully!');
    }

    public function editSection(DocSection $section)
    {
        $categories = DocCategory::active()->orderBy('sort_order')->get();
        return view('admin.documentation.sections.edit', compact('section', 'categories'));
    }

    public function updateSection(Request $request, DocSection $section)
    {
        $request->validate([
            'category_id' => 'required|exists:doc_categories,id',
            'title' => 'required|string|max:255',
            'icon' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'integer|min:0',
        ]);

        $section->update($request->all());

        return redirect()->route('admin.documentation.sections')
                        ->with('success', 'Section updated successfully!');
    }

    public function deleteSection(DocSection $section)
    {
        $section->delete();
        return redirect()->route('admin.documentation.sections')
                        ->with('success', 'Section deleted successfully!');
    }

    // Contents CRUD
    public function contents()
    {
        $contents = DocContent::with(['section.category'])->orderBy('sort_order')->get();
        return view('admin.documentation.contents.index', compact('contents'));
    }

    public function createContent()
    {
        $sections = DocSection::with('category')->active()->orderBy('sort_order')->get();
        $contentTypes = DocContent::getContentTypes();
        return view('admin.documentation.contents.create', compact('sections', 'contentTypes'));
    }

    public function storeContent(Request $request)
    {
        $request->validate([
            'section_id' => 'required|exists:doc_sections,id',
            'title' => 'required|string|max:255',
            'content_type' => 'required|in:' . implode(',', array_keys(DocContent::getContentTypes())),
            'content' => 'required|string',
            'language' => 'nullable|string|max:50',
            'metadata' => 'nullable|json',
            'sort_order' => 'integer|min:0',
        ]);

        $data = $request->all();
        if ($request->metadata) {
            $data['metadata'] = json_decode($request->metadata, true);
        }

        DocContent::create($data);

        return redirect()->route('admin.documentation.contents')
                        ->with('success', 'Content created successfully!');
    }

    public function editContent(DocContent $content)
    {
        $sections = DocSection::with('category')->active()->orderBy('sort_order')->get();
        $contentTypes = DocContent::getContentTypes();
        return view('admin.documentation.contents.edit', compact('content', 'sections', 'contentTypes'));
    }

    public function updateContent(Request $request, DocContent $content)
    {
        $request->validate([
            'section_id' => 'required|exists:doc_sections,id',
            'title' => 'required|string|max:255',
            'content_type' => 'required|in:' . implode(',', array_keys(DocContent::getContentTypes())),
            'content' => 'required|string',
            'language' => 'nullable|string|max:50',
            'metadata' => 'nullable|json',
            'sort_order' => 'integer|min:0',
        ]);

        $data = $request->all();
        if ($request->metadata) {
            $data['metadata'] = json_decode($request->metadata, true);
        }

        $content->update($data);

        return redirect()->route('admin.documentation.contents')
                        ->with('success', 'Content updated successfully!');
    }

    public function deleteContent(DocContent $content)
    {
        $content->delete();
        return redirect()->route('admin.documentation.contents')
                        ->with('success', 'Content deleted successfully!');
    }

    // Preview and Generate
    public function preview()
    {
        $categories = DocCategory::active()
                                ->with(['activeSections.activeContents'])
                                ->ordered()
                                ->get();

        return view('admin.documentation.preview', compact('categories'));
    }

    public function generate()
    {
        $categories = DocCategory::active()
                                ->with(['activeSections.activeContents'])
                                ->ordered()
                                ->get();

        $template = DocTemplate::where('is_default', true)->first();
        
        if (!$template) {
            return redirect()->back()->with('error', 'No default template found!');
        }

        return view('documentation.generated', compact('categories', 'template'));
    }
}
