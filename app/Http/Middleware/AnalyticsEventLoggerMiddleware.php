<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Jobs\LogAnalyticsEventJob;
use App\Transformers\AnalyticsEventTransformer;
use App\Helpers\CleverTapHelper;
class AnalyticsEventLoggerMiddleware
{
    protected $eventData = [];
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $this->eventData = [
            'path' => ltrim($request->path(), '/'),
            'user_id' => Auth::id() ?? $request->input('user_id'),
            'customData' => [
                'ip' => $request->ip(),
                'device' => $request->header('User-Agent'),
            ]
        ];

        return $next($request);
    }

    public function terminate($request, $response)
    {
        try {
            $user_id = Auth::id() ?? $request->input('user_id');
            $statusCode = $response->getStatusCode();
            if ($user_id == '12501981' && $statusCode == 200) {

                $content = $response->getContent();
                $data = json_decode($content, true);
                \Log::info('AnalyticsEventLoggerMiddleware:', [
                    'status' => $statusCode,
                    'request' => $request->all(),

                ]);

                // $path = ltrim($request->path(), '/'); // e.g. api/auth/login
                // $path = preg_replace('/^api\//', '', $path); // remove "api/" prefix 

                $path = preg_replace('/^api\//', '', ltrim($request->path(), '/'));
                $eventConfigs = Config::get("analytics_events.$path");
                \Log::info('$event && $identity:', [
                    'path' => $path,
                    'eventConfigs' => $eventConfigs,
                ]);
                if ($eventConfigs) {
                    $config = $eventConfigs;
                    $event = $config['event'] ?? null;
                    $message = $config['message'] ?? null;
                    $identity = Auth::id() ?? $request->input('user_id');


                    if ($event && $identity) {
                        \Log::info('$event && $identity: sahi chal raha hai ', [
                            'event' => $event,
                            'identity' => $identity,
                        ]);
                        $customData = AnalyticsEventTransformer::getPayload($path, $request, $data);
                        \Log::info('AnalyticsEventTransformer customData', [
                            'customData' => $customData,
                        ]);

                        \Log::info('🔄 MIDDLEWARE - About to dispatch job with:', [
                            'identity' => $identity,
                            'event' => $event,
                            'message' => $message,
                            'customData' => $customData,
                            'customData_type' => gettype($customData),
                            'customData_json' => json_encode($customData)
                        ]);

                        // Insert record into database first
                        $eventId = DB::table('clevertap_events')->insertGetId([
                            'user_identity' => $identity,
                            'event_name' => $event,
                            'event_data' => json_encode($customData),
                            'message' => $message,
                            'status' => 'pending',
                            'job_id' => 'dispatched_' . time(),
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);

                        \Log::info('📝 MIDDLEWARE - Database record created:', ['event_id' => $eventId]);

                        // In middleware - processes asynchronously
                        dispatch(new LogAnalyticsEventJob($identity, $event, $message, $customData));
                    } else {
                        \Log::info('Event or Identity not match');
                    }
                } else {
                    \Log::info('eventConfigs confir error issue');
                }
            }
        } catch (\Throwable $e) {
            \Log::warning('CleverTap terminate middleware error: ' . $e->getMessage());
        }
    }
}
