<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Jobs\LogAnalyticsEventJob;
use Illuminate\Support\Facades\Log;

class CleverTapLoggerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // Store request start time
        $request->attributes->set('request_start_time', microtime(true));

        // Extract custom headers
        $customHeaders = $this->extractCustomHeaders($request);
        $request->attributes->set('custom_headers', $customHeaders);

        // Process the request
        $response = $next($request);

        // Log after response is ready (terminate method will be called)
        return $response;
    }

    /**
     * Handle tasks after the response has been sent to the browser.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Http\Response  $response
     * @return void
     */
    public function terminate($request, $response)
    {
        try {
            // Calculate request duration
            $startTime = $request->attributes->get('request_start_time', microtime(true));
            $duration = round((microtime(true) - $startTime) * 1000, 2); // in milliseconds

            // Get custom headers
            $customHeaders = $request->attributes->get('custom_headers', []);

            // Prepare CleverTap event data
            $eventData = $this->prepareEventData($request, $response, $duration, $customHeaders);

            // Dispatch to queue
            dispatch(new LogAnalyticsEventJob(
                $eventData['user_id'],
                $eventData['event_name'],
                'Team player view event from middleware',
                $eventData['event_data']
            ));

            Log::info('CleverTap event queued successfully', [
                'event' => 'team_player_view',
                'user_id' => $eventData['user_id'] ?? 'unknown',
                'duration' => $duration . 'ms'
            ]);

        } catch (\Exception $e) {
            Log::error('CleverTap middleware error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Extract custom headers from request
     *
     * @param Request $request
     * @return array
     */
    private function extractCustomHeaders(Request $request): array
    {
        $customHeaders = [];

        // Define headers to extract
        $headersToExtract = [
            'X-User-ID',
            'X-Session-ID',
            'X-Device-ID',
            'X-App-Version',
            'X-Platform',
            'X-Team-ID',
            'X-Player-Role',
            'X-Game-Mode',
            'X-Client-IP',
            'X-Request-ID'
        ];

        foreach ($headersToExtract as $header) {
            if ($request->hasHeader($header)) {
                $customHeaders[strtolower(str_replace('-', '_', $header))] = $request->header($header);
            }
        }

        // Also extract some standard headers
        $standardHeaders = [
            'User-Agent' => 'user_agent',
            'Accept-Language' => 'accept_language',
            'Referer' => 'referer'
        ];

        foreach ($standardHeaders as $header => $key) {
            if ($request->hasHeader($header)) {
                $customHeaders[$key] = $request->header($header);
            }
        }

        return $customHeaders;
    }

    /**
     * Prepare event data for CleverTap
     *
     * @param Request $request
     * @param Response $response
     * @param float $duration
     * @param array $customHeaders
     * @return array
     */
    private function prepareEventData(Request $request, $response, float $duration, array $customHeaders): array
    {
        // Extract user ID from various sources
        $userId = $this->extractUserId($request, $customHeaders);

        // Get route information
        $route = $request->route();
        $routeName = $route ? $route->getName() : 'unknown';
        $routeAction = $route ? $route->getActionName() : 'unknown';

        // Prepare base event data
        $eventData = [
            'user_id' => $userId,
            'event_name' => 'team_player_view',
            'event_data' => [
                // Request Information
                'request_method' => $request->getMethod(),
                'request_url' => $request->fullUrl(),
                'request_path' => $request->getPathInfo(),
                'route_name' => $routeName,
                'route_action' => $routeAction,

                // Response Information
                'response_status' => $response->getStatusCode(),
                'response_time_ms' => $duration,

                // Static Data for team_player_view event
                'event_type' => 'api_call',
                'source' => 'user_api',
                'category' => 'user_interaction',
                'action' => 'view_player_details',

                // Timestamp Information
                'timestamp' => now()->toISOString(),
                'server_time' => time(),
                'timezone' => config('app.timezone', 'UTC'),

                // Session Information
                'session_id' => $customHeaders['x_session_id'] ?? session()->getId(),
                'request_id' => $customHeaders['x_request_id'] ?? uniqid('req_'),

                // Device/Client Information
                'device_id' => $customHeaders['x_device_id'] ?? null,
                'app_version' => $customHeaders['x_app_version'] ?? null,
                'platform' => $customHeaders['x_platform'] ?? 'web',
                'user_agent' => $customHeaders['user_agent'] ?? null,
                'client_ip' => $request->ip(),

                // Team/Game Information
                'team_id' => $customHeaders['x_team_id'] ?? null,
                'player_role' => $customHeaders['x_player_role'] ?? null,
                'game_mode' => $customHeaders['x_game_mode'] ?? null,

                // Additional Context
                'language' => $customHeaders['accept_language'] ?? 'en',
                'referer' => $customHeaders['referer'] ?? null,

                // Performance Metrics
                'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
                'peak_memory' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB'
            ]
        ];

        // Add request payload for POST/PUT requests
        if (in_array($request->getMethod(), ['POST', 'PUT', 'PATCH'])) {
            $eventData['event_data']['request_payload_size'] = strlen(json_encode($request->all()));

            // Add specific fields if they exist (without sensitive data)
            $safeFields = ['user_id', 'team_id', 'game_id', 'match_id', 'sport_key'];
            foreach ($safeFields as $field) {
                if ($request->has($field)) {
                    $eventData['event_data']['request_' . $field] = $request->input($field);
                }
            }
        }

        return $eventData;
    }

    /**
     * Extract user ID from request
     *
     * @param Request $request
     * @param array $customHeaders
     * @return string
     */
    private function extractUserId(Request $request, array $customHeaders): string
    {
        // Try to get user ID from various sources
        $userId = $customHeaders['x_user_id'] ??
                  $request->input('user_id') ??
                  $request->route('user_id') ??
                  $request->route('id') ??
                  'anonymous_' . uniqid();

        return (string) $userId;
    }
}
