<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Helpers\CleverTapHelper;
use Illuminate\Support\Facades\DB;

class LogAnalyticsEventJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 10;


    protected $identity;
    protected $event;
    protected $message;
    protected $customData;


    /**
     * Create a new job instance.
     */
    public function __construct($identity, $event, $message = null, $customData = [])
    {
        $this->identity = $identity;
        $this->event = $event;
        $this->message = $message;
        $this->customData = $customData;
    }


//     public function handle(): void
// {
//     try {
//         \Log::info('customData value:', ['customData' => $this->customData]);

//         $eventData = is_array($this->customData) ? $this->customData : [];

//         if ($this->message) {
//             $eventData['message'] = $this->message;
//         }

//         // $eventData['timestamp'] = now()->toIso8601String();

//         // ✅ Actual API call with logging
//         $response = CleverTapHelper::sendEvent($this->identity, $this->event, $eventData);

//         // ✅ Log response
//         \Log::info("CleverTap API Response:", $response);
//         \Log::info("LogAnalyticsEventJobCleverTap Event Logged: {$this->event} for user {$this->identity}");
//     } catch (\Exception $e) {
//         \Log::warning('LogAnalyticsEventJobCleverTap Event Job Failed: ' . $e->getMessage());
//         throw $e;
//     }
// }

 public function handle(): void
{
    try {
        \Log::info('🚀 JOB STARTED - LogAnalyticsEventJob', [
            'identity' => $this->identity,
            'event' => $this->event,
            'message' => $this->message,
            'customData' => $this->customData,
            'customData_type' => gettype($this->customData),
            'customData_json' => json_encode($this->customData)
        ]);

        // Use the exact same call as the working direct call
        \Log::info('📞 CALLING CleverTapHelper::sendEvent with:', [
            'identity' => $this->identity,
            'event' => $this->event,
            'customData' => $this->customData
        ]);

        $response = CleverTapHelper::sendEvent($this->identity, $this->event, $this->customData);

        // Update database status
        DB::table('clevertap_events')
            ->where('user_identity', $this->identity)
            ->where('event_name', $this->event)
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->limit(1)
            ->update([
                'status' => $response['success'] ? 'sent' : 'failed',
                'response_data' => json_encode($response),
                'sent_at' => now(),
                'updated_at' => now()
            ]);

        // ✅ Log response
        \Log::info("✅ CleverTap API Response from JOB:", $response);
        \Log::info("🎉 LogAnalyticsEventJobCleverTap Event Logged: {$this->event} for user {$this->identity}");
    } catch (\Exception $e) {
        \Log::error('❌ LogAnalyticsEventJobCleverTap Event Job Failed: ' . $e->getMessage(), [
            'trace' => $e->getTraceAsString(),
            'identity' => $this->identity,
            'event' => $this->event,
            'customData' => $this->customData
        ]);
        throw $e;
    }
}


    public function tags(): array
    {
        return ['clevertap', 'event:' . $this->event];
    }

    public function failed(\Throwable $exception): void
    {
        \Log::error("CleverTap Job Failed after retries", [
            'identity' => $this->identity,
            'event' => $this->event,
            'error' => $exception->getMessage()
        ]);
    }
}
