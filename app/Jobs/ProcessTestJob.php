<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessTestJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;

    /**
     * Create a new job instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
   public function handle()
{
    try {
        \Log::info('✅ Queue Job Running:', ['data' => $this->data]);
    } catch (\Throwable $e) {
        \Log::error('❌ Job Failed with Exception: ' . $e->getMessage(), [
            'trace' => $e->getTraceAsString(),
            'data' => $this->data,
        ]);
        throw $e; // Laravel ko batane ke liye fail hua
    }
}
}
