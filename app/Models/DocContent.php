<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DocContent extends Model
{
    protected $table = 'doc_contents';
    
    protected $fillable = [
        'section_id',
        'title',
        'slug',
        'content_type',
        'content',
        'language',
        'metadata',
        'sort_order',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'section_id' => 'integer',
        'metadata' => 'array'
    ];

    // Content types
    const TYPE_TEXT = 'text';
    const TYPE_CODE = 'code';
    const TYPE_COMMAND = 'command';
    const TYPE_RESPONSE = 'response';
    const TYPE_ERROR = 'error';
    const TYPE_WARNING = 'warning';
    const TYPE_INFO = 'info';
    const TYPE_TABLE = 'table';
    const TYPE_LIST = 'list';

    public static function getContentTypes()
    {
        return [
            self::TYPE_TEXT => 'Text Content',
            self::TYPE_CODE => 'Code Block',
            self::TYPE_COMMAND => 'Command',
            self::TYPE_RESPONSE => 'Response',
            self::TYPE_ERROR => 'Error Message',
            self::TYPE_WARNING => 'Warning',
            self::TYPE_INFO => 'Information',
            self::TYPE_TABLE => 'Table',
            self::TYPE_LIST => 'List'
        ];
    }

    // Relationships
    public function section()
    {
        return $this->belongsTo(DocSection::class, 'section_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('content_type', $type);
    }

    public function scopeBySection($query, $sectionId)
    {
        return $query->where('section_id', $sectionId);
    }

    // Mutators
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;
        $this->attributes['slug'] = str_slug($value);
    }

    // Accessors
    public function getFormattedContentAttribute()
    {
        switch ($this->content_type) {
            case self::TYPE_CODE:
                return $this->formatCodeBlock();
            case self::TYPE_COMMAND:
                return $this->formatCommand();
            case self::TYPE_RESPONSE:
                return $this->formatResponse();
            case self::TYPE_ERROR:
                return $this->formatError();
            case self::TYPE_WARNING:
                return $this->formatWarning();
            case self::TYPE_INFO:
                return $this->formatInfo();
            case self::TYPE_TABLE:
                return $this->formatTable();
            case self::TYPE_LIST:
                return $this->formatList();
            default:
                return $this->formatText();
        }
    }

    // Formatting methods
    private function formatCodeBlock()
    {
        $language = $this->language ?: 'text';
        return '<div class="code-block" data-lang="' . $language . '"><pre><code>' . htmlspecialchars($this->content) . '</code></pre></div>';
    }

    private function formatCommand()
    {
        return '<div class="command">' . htmlspecialchars($this->content) . '</div>';
    }

    private function formatResponse()
    {
        return '<div class="response-block">' . htmlspecialchars($this->content) . '</div>';
    }

    private function formatError()
    {
        return '<div class="error-block">' . htmlspecialchars($this->content) . '</div>';
    }

    private function formatWarning()
    {
        return '<div class="warning-indicator"><i class="fas fa-exclamation-triangle"></i><div>' . htmlspecialchars($this->content) . '</div></div>';
    }

    private function formatInfo()
    {
        return '<div class="info-indicator"><i class="fas fa-info-circle"></i><div>' . htmlspecialchars($this->content) . '</div></div>';
    }

    private function formatTable()
    {
        $data = $this->metadata;
        if (!$data || !isset($data['headers']) || !isset($data['rows'])) {
            return '<p>Invalid table data</p>';
        }

        $html = '<div class="table-container"><table>';
        
        // Headers
        $html .= '<thead><tr>';
        foreach ($data['headers'] as $header) {
            $html .= '<th>' . htmlspecialchars($header) . '</th>';
        }
        $html .= '</tr></thead>';
        
        // Rows
        $html .= '<tbody>';
        foreach ($data['rows'] as $row) {
            $html .= '<tr>';
            foreach ($row as $cell) {
                $html .= '<td>' . htmlspecialchars($cell) . '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody></table></div>';
        
        return $html;
    }

    private function formatList()
    {
        $items = explode("\n", $this->content);
        $html = '<ul>';
        foreach ($items as $item) {
            if (trim($item)) {
                $html .= '<li>' . htmlspecialchars(trim($item)) . '</li>';
            }
        }
        $html .= '</ul>';
        return $html;
    }

    private function formatText()
    {
        return '<div class="text-content">' . nl2br(htmlspecialchars($this->content)) . '</div>';
    }
}
