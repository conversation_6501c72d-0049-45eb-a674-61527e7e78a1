<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DocSection extends Model
{
    protected $table = 'doc_sections';
    
    protected $fillable = [
        'category_id',
        'title',
        'slug',
        'icon',
        'description',
        'sort_order',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'category_id' => 'integer'
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(DocCategory::class, 'category_id');
    }

    public function contents()
    {
        return $this->hasMany(DocContent::class, 'section_id')->orderBy('sort_order');
    }

    public function activeContents()
    {
        return $this->hasMany(DocContent::class, 'section_id')
                    ->where('is_active', true)
                    ->orderBy('sort_order');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    // Mutators
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = $value;
        $this->attributes['slug'] = str_slug($value);
    }

    // Accessors
    public function getIconHtmlAttribute()
    {
        return $this->icon ? '<i class="' . $this->icon . '"></i>' : '';
    }

    public function getFullTitleAttribute()
    {
        return $this->category->name . ' - ' . $this->title;
    }

    // Methods
    public function getContentByType($type)
    {
        return $this->contents()->where('content_type', $type)->get();
    }

    public function hasContentType($type)
    {
        return $this->contents()->where('content_type', $type)->exists();
    }
}
