<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DocCategory extends Model
{
    protected $table = 'doc_categories';
    
    protected $fillable = [
        'name',
        'slug',
        'icon',
        'description',
        'sort_order',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    // Relationships
    public function sections()
    {
        return $this->hasMany(DocSection::class, 'category_id')->orderBy('sort_order');
    }

    public function activeSections()
    {
        return $this->hasMany(DocSection::class, 'category_id')
                    ->where('is_active', true)
                    ->orderBy('sort_order');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    // Mutators
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = $value;
        $this->attributes['slug'] = str_slug($value);
    }

    // Accessors
    public function getIconHtmlAttribute()
    {
        return $this->icon ? '<i class="' . $this->icon . '"></i>' : '';
    }

    // Methods
    public function getTotalContents()
    {
        return $this->sections()
                    ->with('contents')
                    ->get()
                    ->sum(function($section) {
                        return $section->contents->count();
                    });
    }
}
