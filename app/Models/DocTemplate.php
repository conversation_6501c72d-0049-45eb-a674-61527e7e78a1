<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DocTemplate extends Model
{
    protected $table = 'doc_templates';
    
    protected $fillable = [
        'name',
        'slug',
        'description',
        'html_template',
        'css_styles',
        'js_scripts',
        'is_default',
        'is_active'
    ];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean'
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    // Mutators
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = $value;
        $this->attributes['slug'] = str_slug($value);
    }

    // Methods
    public static function getDefault()
    {
        return static::where('is_default', true)->first();
    }
}
