<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use App\Helpers\Helpers;

class TradingWalletTdsService
{
    public function calculateTdsAndCharges($user_id, $amount, $type)
    {
        $originalAmount = $amount;
        $session_year = Helpers::getSessionYear(date("Y-m-d"));

        $registerusers_session = DB::table("registerusers_session")
            ->where("user_id", $user_id)
            ->where("session_year", $session_year)
            ->first();

        $tds_amount = 0;
        $instant_charge = 0;
        $cashback_deduction = 0;

        // Instant withdrawal charge

        // if ($type == 'bank_instant') {
        //     if ($amount > 1000) {
        //         $amount = $amount - ($amount * 0.01);
        //     } else {
        //         $amount = $amount - 10;
        //     }
        // }
        if ($type == 'bank_instant') {
            if ($amount > 1000) {
                $instant_charge = $amount * 0.01;
                $amount = $amount - ($amount * 0.01);
            } else {
                $instant_charge = 10;
                 $amount = $amount - 10;
            }
        }
        // dd($amount);die;
        if ($registerusers_session) {

            // Cashback deduction
            $cashback_deduction = $amount * 0.15;
            $amount = $amount - $cashback_deduction;

            $total_tds_dedcuted = $registerusers_session->total_tds_dedcuted ?? 0;
            $total_withdraw = $registerusers_session->total_withdraw ?? 0;
            $total_trading_withdraw = $registerusers_session->total_trading_withdraw ?? 0;
            $total_investment = (
                $registerusers_session->total_opening_balance +
                $registerusers_session->total_deposit +
                $registerusers_session->total_commission
            );


            $netwinning = ($total_withdraw + $amount + $total_trading_withdraw) - $total_investment;

            if ($netwinning > 0) {
                $tds_amount = $netwinning * 0.3;
                if ($total_tds_dedcuted >= $tds_amount) {
                    $tds_amount = 0;
                } else {
                    $tds_amount -= $total_tds_dedcuted;
                }
            }




            // Final TDS deduction from remaining
            if ($tds_amount > 0) {
                if (($amount * 0.3) >= $tds_amount) {
                    $amount = $amount - $tds_amount;
                } else {
                    $tds_amount = $amount * 0.3;
                    $amount = $amount - $tds_amount;
                }
            }
        }

        if ($tds_amount < 0) $tds_amount = 0;

        return [
            'final_amount' => $amount,
            'tds_amount' => $tds_amount,
            'instant_charge' => $instant_charge,
            'cashback_deduction' => $cashback_deduction,
            'session' => $registerusers_session
        ];
    }
}
