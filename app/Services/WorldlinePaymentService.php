<?php

namespace App\Services;

use DateTime;
use DateTimeZone;

class WorldlinePaymentService
{
    public function generateAuthorizationData(string $httpMethod, string $uriResource): array
    {
        $apiKey     ="7691ED32DBE625F6C9BE";
        $apiSecret  = 'm8iaDYNWLNB8mD7RtGdnEMbCaGYFBtWg7gB7e9xkGCDZSs4RJLDMgMjV4H3QTTf99x0l++4jVas4NU4PtT3gSw==';
        $pspId      = "RG";
        $apiEndpoint = 'https://payment.preprod.direct.worldline-solutions.com';

        $contentType = $httpMethod === 'POST' ? 'application/json; charset=utf-8' : '';
        $dateTime = $this->getCurrentTime();

        $stringToHash = $this->createStringToHash($dateTime, $pspId, $httpMethod, $contentType, $uriResource);
        $signature = $this->createHash($stringToHash, $apiSecret);
        $authorizationHeader = "GCS v1HMAC:{$apiKey}:{$signature}";

        return [
            'api_key' => $apiKey,
            'signature' => $signature,
            'authorization_header' => $authorizationHeader,
            'headers' => [
                'Content-Type' => $contentType,
                'Date' => $dateTime
            ],
            'url' => "{$apiEndpoint}/v2/{$pspId}{$uriResource}",
            'string_to_hash' => $stringToHash,
        ];
    }

    private function getCurrentTime(): string
    {
        $tz = new DateTimeZone('GMT');
        $dt = new DateTime('now', $tz);
        return $dt->format('D, d M Y H:i:s T');
    }

    private function createStringToHash(string $dateTime, string $pspId, string $requestMethod, string $contentType, string $uriResource): string
    {
        $endpointUrl = "/v2/{$pspId}{$uriResource}";
        return "{$requestMethod}\n{$contentType}\n{$dateTime}\n{$endpointUrl}\n";
    }

    private function createHash(string $stringToHash, string $apiSecret): string
    {
        $hash = hash_hmac('sha256', $stringToHash, $apiSecret, true);
        return base64_encode($hash);
    }

    // public function testApiConfiguration(string $httpMethod, string $uriResource, ?array $payload = null): array
    // {
    //     // Generate authorization data
    //     $authData = $this->generateAuthorizationData($httpMethod, $uriResource);

    //     // Initialize cURL
    //     $ch = curl_init();
    //     curl_setopt($ch, CURLOPT_URL, $authData['url']);
    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    //     // Set headers
    //     $headers = [
    //         'Authorization: ' . $authData['authorization_header'],
    //         'Content-Type: ' . $authData['headers']['Content-Type'],
    //         'Date: ' . $authData['headers']['Date']
    //     ];

    //     // Handle POST request if applicable
    //     if ($httpMethod === 'POST' && $payload !== null) {
    //         curl_setopt($ch, CURLOPT_POST, true);
    //         curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
    //     }

    //     curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    //     // Execute the request
    //     $response = curl_exec($ch);
    //     $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    //     curl_close($ch);

    //     // Return the status and response
    //     return [
    //         'status' => $httpCode,
    //         'response' => $response,
    //         'authorization_header' => $authData['authorization_header'], // For debugging
    //         'string_to_hash' => $authData['string_to_hash'], // For debugging
    //         'date' => $authData['headers']['Date'] // For debugging
    //     ];
    // }
    public function testApiConfiguration(string $httpMethod, string $uriResource, ?array $payload = null): array
    {
        // Generate authorization data
        $authData = $this->generateAuthorizationData($httpMethod, $uriResource);
        // Initialize cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $authData['url']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Set headers
        $headers = [
            'Authorization: ' . $authData['authorization_header'],
            'Content-Type: ' . $authData['headers']['Content-Type'],
            'Date: ' . $authData['headers']['Date']
        ];

        // Handle POST request if applicable
        if ($httpMethod === 'POST' && $payload !== null) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        }

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // Return the status and response
        return [
            'status' => $httpCode,
            'response' => $response,
            'authorization_header' => $authData['authorization_header'],
            'string_to_hash' => $authData['string_to_hash'],
            'date' => $authData['headers']['Date']
        ];
    }
}
