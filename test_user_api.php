<?php

/**
 * Test script for User API with CleverTap Middleware
 * 
 * This script tests the new User API endpoints with custom headers
 * and verifies that CleverTap events are being logged properly.
 */

// Base URL for your Laravel application
$baseUrl = 'http://localhost:8000/api';

// Test data
$testUserId = '12501981';
$testTeamId = 'team_alpha_001';
$testSessionId = 'session_' . uniqid();
$testRequestId = 'req_' . uniqid();

// Custom headers to test middleware
$customHeaders = [
    'Content-Type: application/json',
    'Accept: application/json',
    'X-User-ID: ' . $testUserId,
    'X-Session-ID: ' . $testSessionId,
    'X-Device-ID: device_' . uniqid(),
    'X-App-Version: 1.2.3',
    'X-Platform: mobile_app',
    'X-Team-ID: ' . $testTeamId,
    'X-Player-Role: captain',
    'X-Game-Mode: tournament',
    'X-Request-ID: ' . $testRequestId,
    'User-Agent: TestApp/1.0 (iOS; iPhone 12)',
    'Accept-Language: en-US,en;q=0.9'
];

echo "🚀 Testing User API with CleverTap Middleware\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Test 1: Get User Details
echo "📋 Test 1: Get User Details\n";
echo "-" . str_repeat("-", 30) . "\n";

$userData = [
    'user_id' => $testUserId
];

$response1 = makeApiCall($baseUrl . '/users/details', $userData, $customHeaders);
echo "Response: " . $response1 . "\n\n";

// Test 2: Get Multiple Users
echo "👥 Test 2: Get Multiple Users\n";
echo "-" . str_repeat("-", 30) . "\n";

$multipleUsersData = [
    'user_ids' => [$testUserId, 'user_002', 'user_003']
];

$response2 = makeApiCall($baseUrl . '/users/multiple', $multipleUsersData, $customHeaders);
echo "Response: " . $response2 . "\n\n";

// Test 3: Get User Profile with Stats
echo "📊 Test 3: Get User Profile with Stats\n";
echo "-" . str_repeat("-", 30) . "\n";

$profileData = [
    'user_id' => $testUserId,
    'include_stats' => true,
    'include_preferences' => true
];

$response3 = makeApiCall($baseUrl . '/users/profile', $profileData, $customHeaders);
echo "Response: " . $response3 . "\n\n";

// Test 4: Search Users
echo "🔍 Test 4: Search Users\n";
echo "-" . str_repeat("-", 30) . "\n";

$searchData = [
    'query' => 'john',
    'limit' => 5
];

$response4 = makeApiCall($baseUrl . '/users/search', $searchData, $customHeaders);
echo "Response: " . $response4 . "\n\n";

// Test 5: Team Players (Additional endpoint)
echo "🏆 Test 5: Team Players\n";
echo "-" . str_repeat("-", 30) . "\n";

$teamData = [
    'team_id' => $testTeamId,
    'user_id' => $testUserId
];

$response5 = makeApiCall($baseUrl . '/team/players', $teamData, $customHeaders);
echo "Response: " . $response5 . "\n\n";

// Test 6: Player Stats
echo "📈 Test 6: Player Stats\n";
echo "-" . str_repeat("-", 30) . "\n";

$statsData = [
    'player_id' => $testUserId,
    'user_id' => $testUserId
];

$response6 = makeApiCall($baseUrl . '/player/stats', $statsData, $customHeaders);
echo "Response: " . $response6 . "\n\n";

// Test 7: Game History
echo "🎮 Test 7: Game History\n";
echo "-" . str_repeat("-", 30) . "\n";

$historyData = [
    'user_id' => $testUserId,
    'limit' => 5
];

$response7 = makeApiCall($baseUrl . '/game/history', $historyData, $customHeaders);
echo "Response: " . $response7 . "\n\n";

echo "✅ All tests completed!\n";
echo "🔍 Check your Laravel logs and CleverTap events queue for 'team_player_view' events.\n";
echo "📊 Each API call should have generated a CleverTap event with custom headers.\n\n";

echo "📋 Test Summary:\n";
echo "- User ID: {$testUserId}\n";
echo "- Team ID: {$testTeamId}\n";
echo "- Session ID: {$testSessionId}\n";
echo "- Request ID: {$testRequestId}\n";
echo "- Total API calls: 7\n";
echo "- Expected CleverTap events: 7 (all with event_name: 'team_player_view')\n\n";

echo "🔧 To verify CleverTap events:\n";
echo "1. Check Laravel logs: tail -f storage/logs/laravel.log\n";
echo "2. Check queue jobs: php artisan queue:work redis --once\n";
echo "3. Check CleverTap events table: SELECT * FROM clevertap_events ORDER BY created_at DESC LIMIT 10;\n";

/**
 * Make API call with custom headers
 */
function makeApiCall($url, $data, $headers) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return "❌ CURL Error: " . $error;
    }
    
    if ($httpCode !== 200) {
        return "❌ HTTP {$httpCode}: " . $response;
    }
    
    // Pretty print JSON response
    $decoded = json_decode($response, true);
    if ($decoded) {
        return "✅ HTTP {$httpCode}: " . json_encode($decoded, JSON_PRETTY_PRINT);
    }
    
    return "✅ HTTP {$httpCode}: " . $response;
}
