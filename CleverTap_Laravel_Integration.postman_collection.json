{"info": {"name": "CleverTap Laravel Integration", "description": "Complete API collection for Laravel 5.6 + Redis Queue + CleverTap integration with comprehensive testing scenarios", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string", "description": "Base URL for the Laravel application"}, {"key": "test_user_id", "value": "12501981", "type": "string", "description": "Test user ID for API calls"}], "item": [{"name": "🧪 CleverTap Integration Tests", "description": "Core CleverTap integration endpoints", "item": [{"name": "Test Queue Integration", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test('Response contains event data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('event_id');", "    pm.expect(jsonData.data).to.have.property('queue_status');", "});", "", "// Store event ID for later use", "if (pm.response.json().success) {", "    pm.collectionVariables.set('last_event_id', pm.response.json().data.event_id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{test_user_id}}\",\n  \"event\": \"postman_test_event\",\n  \"message\": \"Testing CleverTap integration from Postman\",\n  \"custom_data\": {\n    \"source\": \"postman\",\n    \"test_type\": \"integration\",\n    \"timestamp\": \"{{$isoTimestamp}}\",\n    \"random_id\": \"{{$randomUUID}}\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/clevertap/test-queue", "host": ["{{base_url}}"], "path": ["api", "clevertap", "test-queue"]}, "description": "Send a test event to CleverTap queue for processing"}}, {"name": "Get Events History", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains events data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('total');", "});", "", "pm.test('Events have required fields', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.data.length > 0) {", "        const event = jsonData.data[0];", "        pm.expect(event).to.have.property('id');", "        pm.expect(event).to.have.property('user_identity');", "        pm.expect(event).to.have.property('event_name');", "        pm.expect(event).to.have.property('status');", "    }", "});"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/clevertap/events-history?limit=10", "host": ["{{base_url}}"], "path": ["api", "clevertap", "events-history"], "query": [{"key": "limit", "value": "10", "description": "Number of events to retrieve"}, {"key": "status", "value": "sent", "disabled": true, "description": "Filter by event status"}, {"key": "user_id", "value": "{{test_user_id}}", "disabled": true, "description": "Filter by user ID"}]}, "description": "Retrieve CleverTap events history with optional filtering"}}, {"name": "Update CleverTap Settings", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Settings updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData).to.have.property('message');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"project_id\": \"TEST-549-894-847Z\",\n  \"project_token\": \"TEST-4cb-c45\",\n  \"passcode\": \"YAS-KUA-CAEL\",\n  \"region\": \"in1\"\n}"}, "url": {"raw": "{{base_url}}/api/clevertap/update-settings", "host": ["{{base_url}}"], "path": ["api", "clevertap", "update-settings"]}, "description": "Update CleverTap API credentials and settings"}}]}, {"name": "🎮 Middleware Testing", "description": "Test automatic CleverTap event triggering via middleware", "item": [{"name": "Get Player List (Auto CleverTap)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Player list retrieved', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('players');", "});", "", "// Wait for middleware to process", "setTimeout(function() {", "    console.log('CleverTap event should be automatically triggered by middleware');", "}, 1000);"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"{{test_user_id}}\",\n  \"matchkey\": \"89823\",\n  \"sport_key\": \"CRICKET\",\n  \"challenge_id\": \"0\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/getplayerlist", "host": ["{{base_url}}"], "path": ["api", "auth", "getplayerlist"]}, "description": "Test endpoint that automatically triggers CleverTap event via middleware"}}]}, {"name": "🔍 System Health & Monitoring", "description": "System health checks and monitoring endpoints", "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('System is healthy', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('checks');", "});", "", "pm.test('All services are operational', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.checks) {", "        Object.keys(jsonData.checks).forEach(service => {", "            console.log(`${service}: ${jsonData.checks[service].status}`);", "        });", "    }", "});"]}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/health/clevertap", "host": ["{{base_url}}"], "path": ["api", "health", "clevertap"]}, "description": "Check overall system health including Redis, database, and queue workers"}}, {"name": "Get Recent Events", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/clevertap/events-history?limit=5", "host": ["{{base_url}}"], "path": ["api", "clevertap", "events-history"], "query": [{"key": "limit", "value": "5"}]}, "description": "Get the 5 most recent events for monitoring"}}, {"name": "Get Failed Events", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/clevertap/events-history?status=failed&limit=10", "host": ["{{base_url}}"], "path": ["api", "clevertap", "events-history"], "query": [{"key": "status", "value": "failed"}, {"key": "limit", "value": "10"}]}, "description": "Get failed events for troubleshooting"}}]}]}