<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>{{ $settings['title'] ?? 'Documentation' }}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    @if($template && $template->css_styles)
        <style>{!! $template->css_styles !!}</style>
    @else
        <style>
            /* Include the same CSS from our HTML documentation */
            @include('documentation.partials.default-styles')
        </style>
    @endif
</head>
<body>
    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>
    
    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" onclick="closeSidebar()"></div>
    
    <div class="container">
        <!-- Sidebar Navigation -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3>📋 Navigation</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    @foreach($categories as $category)
                        @if($category->activeSections->count() > 0)
                            <li class="nav-category">
                                <span class="category-title">
                                    {!! $category->icon_html !!} {{ $category->name }}
                                </span>
                                <ul class="nav-sections">
                                    @foreach($category->activeSections as $section)
                                        <li>
                                            <a href="#{{ $section->slug }}" onclick="setActive(this)">
                                                {!! $section->icon_html !!} {{ $section->title }}
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </li>
                        @endif
                    @endforeach
                </ul>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <h1>{{ $settings['title'] ?? 'Documentation' }}</h1>
                <p>{{ $settings['subtitle'] ?? 'Complete Setup Guide' }}</p>
                @if(isset($settings['version']))
                    <p style="margin-top: 10px; font-size: 1em;">Version {{ $settings['version'] }} - Last updated: {{ $settings['last_updated'] }}</p>
                @endif
            </div>

            <!-- Search Bar -->
            <div class="search-container">
                <form action="{{ route('documentation.search') }}" method="GET" class="search-form">
                    <input type="text" name="q" placeholder="Search documentation..." class="search-input" value="{{ request('q') }}">
                    <button type="submit" class="search-btn"><i class="fas fa-search"></i></button>
                </form>
            </div>

            <!-- Documentation Content -->
            @foreach($categories as $category)
                @if($category->activeSections->count() > 0)
                    <div class="category-section" id="category-{{ $category->slug }}">
                        <h2 class="category-header">
                            {!! $category->icon_html !!} {{ $category->name }}
                        </h2>
                        @if($category->description)
                            <p class="category-description">{{ $category->description }}</p>
                        @endif

                        @foreach($category->activeSections as $section)
                            <div id="{{ $section->slug }}" class="section">
                                <div class="section-header">
                                    {!! $section->icon_html !!}
                                    <h3>{{ $section->title }}</h3>
                                </div>
                                <div class="section-content">
                                    @if($section->description)
                                        <p class="section-description">{{ $section->description }}</p>
                                    @endif

                                    @foreach($section->activeContents as $content)
                                        <div class="content-item" data-type="{{ $content->content_type }}">
                                            @if($content->title)
                                                <h4 class="content-title">{{ $content->title }}</h4>
                                            @endif
                                            
                                            <div class="content-body">
                                                {!! $content->formatted_content !!}
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            @endforeach

            <!-- Export Options -->
            <div class="export-section">
                <h3>Export Documentation</h3>
                <div class="export-buttons">
                    <a href="{{ route('documentation.export', 'html') }}" class="export-btn">
                        <i class="fas fa-file-code"></i> Export as HTML
                    </a>
                    <a href="{{ route('documentation.export', 'pdf') }}" class="export-btn">
                        <i class="fas fa-file-pdf"></i> Export as PDF
                    </a>
                    <a href="{{ route('documentation.export', 'markdown') }}" class="export-btn">
                        <i class="fas fa-file-alt"></i> Export as Markdown
                    </a>
                    <a href="{{ route('documentation.api') }}" class="export-btn">
                        <i class="fas fa-code"></i> API JSON
                    </a>
                </div>
            </div>

            <!-- Footer -->
            <div class="footer">
                <h3>🎉 Documentation Complete!</h3>
                <p>Generated dynamically using Laravel CRUD system.</p>
                
                <div class="footer-stats">
                    <div class="stat-item">
                        <strong>{{ $categories->count() }}</strong>
                        <span>Categories</span>
                    </div>
                    <div class="stat-item">
                        <strong>{{ $categories->sum(function($cat) { return $cat->activeSections->count(); }) }}</strong>
                        <span>Sections</span>
                    </div>
                    <div class="stat-item">
                        <strong>{{ $categories->sum(function($cat) { return $cat->activeSections->sum(function($section) { return $section->activeContents->count(); }); }) }}</strong>
                        <span>Content Items</span>
                    </div>
                </div>
                
                <div class="footer-meta">
                    <p><strong>Generated:</strong> {{ now()->format('F j, Y \a\t g:i A') }}</p>
                    @if(isset($settings['author']))
                        <p><strong>Author:</strong> {{ $settings['author'] }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @if($template && $template->js_scripts)
        <script>{!! $template->js_scripts !!}</script>
    @else
        @include('documentation.partials.default-scripts')
    @endif
</body>
</html>
