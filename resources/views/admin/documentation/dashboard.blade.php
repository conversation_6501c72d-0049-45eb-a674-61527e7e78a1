@extends('admin.layouts.app')

@section('title', 'Documentation Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-book"></i> Documentation Management
                </h1>
                <div class="page-actions">
                    <a href="{{ route('documentation.index') }}" class="btn btn-primary" target="_blank">
                        <i class="fas fa-eye"></i> View Documentation
                    </a>
                    <a href="{{ route('admin.documentation.preview') }}" class="btn btn-info">
                        <i class="fas fa-search"></i> Preview
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ $stats['categories'] }}</h3>
                    <p>Total Categories</p>
                    <small>{{ $stats['active_categories'] }} active</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-list"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ $stats['sections'] }}</h3>
                    <p>Total Sections</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ $stats['contents'] }}</h3>
                    <p>Content Items</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stat-card">
                <div class="stat-icon bg-info">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ now()->format('M j') }}</h3>
                    <p>Last Updated</p>
                    <small>{{ now()->format('g:i A') }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.documentation.categories.create') }}" class="quick-action-btn">
                                <i class="fas fa-plus-circle"></i>
                                <span>Add Category</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.documentation.sections.create') }}" class="quick-action-btn">
                                <i class="fas fa-plus-square"></i>
                                <span>Add Section</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('admin.documentation.contents.create') }}" class="quick-action-btn">
                                <i class="fas fa-plus"></i>
                                <span>Add Content</span>
                            </a>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <a href="{{ route('documentation.export', 'html') }}" class="quick-action-btn">
                                <i class="fas fa-download"></i>
                                <span>Export HTML</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Management Sections -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-folder"></i> Categories
                    </h5>
                    <a href="{{ route('admin.documentation.categories.index') }}" class="btn btn-sm btn-outline-primary">
                        Manage All
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted">Organize your documentation into logical categories.</p>
                    <div class="d-grid">
                        <a href="{{ route('admin.documentation.categories.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Category
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-list"></i> Sections
                    </h5>
                    <a href="{{ route('admin.documentation.sections.index') }}" class="btn btn-sm btn-outline-primary">
                        Manage All
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted">Create sections within categories for better organization.</p>
                    <div class="d-grid">
                        <a href="{{ route('admin.documentation.sections.create') }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add New Section
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-file-alt"></i> Content
                    </h5>
                    <a href="{{ route('admin.documentation.contents.index') }}" class="btn btn-sm btn-outline-primary">
                        Manage All
                    </a>
                </div>
                <div class="card-body">
                    <p class="text-muted">Add various types of content like text, code, commands, etc.</p>
                    <div class="d-grid">
                        <a href="{{ route('admin.documentation.contents.create') }}" class="btn btn-warning">
                            <i class="fas fa-plus"></i> Add New Content
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Content Updates</h5>
                </div>
                <div class="card-body">
                    @if($recentContents->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Section</th>
                                        <th>Category</th>
                                        <th>Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentContents as $content)
                                        <tr>
                                            <td>
                                                <strong>{{ $content->title }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge badge-{{ $content->content_type == 'code' ? 'primary' : ($content->content_type == 'error' ? 'danger' : 'secondary') }}">
                                                    {{ ucfirst($content->content_type) }}
                                                </span>
                                            </td>
                                            <td>{{ $content->section->title }}</td>
                                            <td>{{ $content->section->category->name }}</td>
                                            <td>{{ $content->updated_at->diffForHumans() }}</td>
                                            <td>
                                                <a href="{{ route('admin.documentation.contents.edit', $content) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No content yet</h5>
                            <p class="text-muted">Start by creating your first category and section.</p>
                            <a href="{{ route('admin.documentation.categories.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create First Category
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 24px;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.stat-content p {
    margin: 0;
    color: #666;
    font-weight: 500;
}

.stat-content small {
    color: #999;
}

.quick-action-btn {
    display: block;
    padding: 20px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    text-decoration: none;
    color: #333;
    text-align: center;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    border-color: #007bff;
    color: #007bff;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,123,255,0.2);
}

.quick-action-btn i {
    display: block;
    font-size: 2rem;
    margin-bottom: 10px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.page-title {
    margin: 0;
    color: #333;
}

.page-actions .btn {
    margin-left: 10px;
}
</style>
@endsection
