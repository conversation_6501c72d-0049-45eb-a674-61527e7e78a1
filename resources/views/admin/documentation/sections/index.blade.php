@extends('admin.layouts.app')

@section('title', 'Sections')

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-list"></i> Documentation Sections
    </h1>
    <div class="page-actions">
        <a href="{{ url('/admin/documentation/sections/create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Section
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">All Sections</h5>
    </div>
    <div class="card-body">
        @if(isset($sections) && $sections->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Icon</th>
                            <th>Contents</th>
                            <th>Sort Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($sections as $section)
                            <tr>
                                <td>
                                    <strong>{{ $section->title }}</strong>
                                    @if($section->description)
                                        <br><small class="text-muted">{{ str_limit($section->description, 50) }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $section->category->name ?? 'N/A' }}</span>
                                </td>
                                <td>
                                    @if($section->icon)
                                        <i class="{{ $section->icon }}"></i>
                                    @else
                                        <span class="text-muted">No icon</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $section->contents->count() }} items</span>
                                </td>
                                <td>{{ $section->sort_order }}</td>
                                <td>
                                    @if($section->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url('/admin/documentation/sections/' . $section->id . '/edit') }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ url('/admin/documentation/sections/' . $section->id) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-list fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No sections found</h5>
                <p class="text-muted">Start by creating your first documentation section.</p>
                <a href="{{ url('/admin/documentation/sections/create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create First Section
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
