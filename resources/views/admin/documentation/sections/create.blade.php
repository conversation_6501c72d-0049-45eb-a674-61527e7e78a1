@extends('admin.layouts.app')

@section('title', 'Create Section')

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-plus"></i> Create New Section
    </h1>
    <div class="page-actions">
        <a href="{{ url('/admin/documentation/sections') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Sections
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Section Details</h5>
            </div>
            <div class="card-body">
                <form action="{{ url('/admin/documentation/sections') }}" method="POST">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="category_id" class="form-label">Category *</label>
                        <select name="category_id" id="category_id" class="form-control" required>
                            <option value="">Select Category</option>
                            @if(isset($categories))
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                        @if($errors->has('category_id'))
                            <div class="text-danger">{{ $errors->first('category_id') }}</div>
                        @endif
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">Section Title *</label>
                        <input type="text" name="title" id="title" class="form-control" value="{{ old('title') }}" required>
                        @if($errors->has('title'))
                            <div class="text-danger">{{ $errors->first('title') }}</div>
                        @endif
                        <small class="form-text text-muted">This will be displayed in the section navigation.</small>
                    </div>

                    <div class="mb-3">
                        <label for="icon" class="form-label">Icon Class</label>
                        <input type="text" name="icon" id="icon" class="form-control" value="{{ old('icon') }}" placeholder="fas fa-server">
                        @if($errors->has('icon'))
                            <div class="text-danger">{{ $errors->first('icon') }}</div>
                        @endif
                        <small class="form-text text-muted">FontAwesome icon class (e.g., fas fa-server, fas fa-code)</small>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea name="description" id="description" class="form-control" rows="3">{{ old('description') }}</textarea>
                        @if($errors->has('description'))
                            <div class="text-danger">{{ $errors->first('description') }}</div>
                        @endif
                        <small class="form-text text-muted">Brief description of what this section contains.</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" name="sort_order" id="sort_order" class="form-control" value="{{ old('sort_order', 0) }}" min="0">
                                @if($errors->has('sort_order'))
                                    <div class="text-danger">{{ $errors->first('sort_order') }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input type="checkbox" name="is_active" id="is_active" class="form-check-input" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label for="is_active" class="form-check-label">Active</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Section
                        </button>
                        <a href="{{ url('/admin/documentation/sections') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Icon Preview</h5>
            </div>
            <div class="card-body text-center">
                <div id="icon_preview" style="font-size: 3rem; color: #007bff; margin: 20px 0;">
                    <i class="fas fa-list"></i>
                </div>
                <p class="text-muted">Icon will appear here</p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title">Popular Section Icons</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-4 text-center mb-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm icon-btn" data-icon="fas fa-server">
                            <i class="fas fa-server"></i>
                        </button>
                        <small class="d-block">Server</small>
                    </div>
                    <div class="col-4 text-center mb-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm icon-btn" data-icon="fas fa-code">
                            <i class="fas fa-code"></i>
                        </button>
                        <small class="d-block">Code</small>
                    </div>
                    <div class="col-4 text-center mb-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm icon-btn" data-icon="fas fa-terminal">
                            <i class="fas fa-terminal"></i>
                        </button>
                        <small class="d-block">Terminal</small>
                    </div>
                    <div class="col-4 text-center mb-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm icon-btn" data-icon="fas fa-database">
                            <i class="fas fa-database"></i>
                        </button>
                        <small class="d-block">Database</small>
                    </div>
                    <div class="col-4 text-center mb-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm icon-btn" data-icon="fas fa-vial">
                            <i class="fas fa-vial"></i>
                        </button>
                        <small class="d-block">Testing</small>
                    </div>
                    <div class="col-4 text-center mb-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm icon-btn" data-icon="fas fa-wrench">
                            <i class="fas fa-wrench"></i>
                        </button>
                        <small class="d-block">Tools</small>
                    </div>
                </div>
            </div>
        </div>

        @if(isset($categories) && $categories->count() > 0)
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title">Available Categories</h5>
            </div>
            <div class="card-body">
                @foreach($categories as $category)
                    <div class="d-flex align-items-center mb-2">
                        @if($category->icon)
                            <i class="{{ $category->icon }} me-2"></i>
                        @endif
                        <span>{{ $category->name }}</span>
                    </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const iconInput = document.getElementById('icon');
    const iconPreview = document.getElementById('icon_preview');
    const iconButtons = document.querySelectorAll('.icon-btn');

    // Update preview when typing
    iconInput.addEventListener('input', function() {
        updateIconPreview(this.value);
    });

    // Icon button clicks
    iconButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const iconClass = this.getAttribute('data-icon');
            iconInput.value = iconClass;
            updateIconPreview(iconClass);
        });
    });

    function updateIconPreview(iconClass) {
        if (iconClass) {
            iconPreview.innerHTML = '<i class="' + iconClass + '"></i>';
        } else {
            iconPreview.innerHTML = '<i class="fas fa-list"></i>';
        }
    }
});
</script>

<style>
.form-actions {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.icon-btn {
    width: 100%;
    aspect-ratio: 1;
}
</style>
@endsection
