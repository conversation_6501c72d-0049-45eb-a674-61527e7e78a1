@extends('admin.layouts.app')

@section('title', 'Contents')

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-file-alt"></i> Documentation Contents
    </h1>
    <div class="page-actions">
        <a href="{{ url('/admin/documentation/contents/create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Content
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">All Content Items</h5>
    </div>
    <div class="card-body">
        @if(isset($contents) && $contents->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Section</th>
                            <th>Category</th>
                            <th>Sort Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($contents as $content)
                            <tr>
                                <td>
                                    <strong>{{ $content->title }}</strong>
                                    <br><small class="text-muted">{{ str_limit(strip_tags($content->content), 50) }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $content->content_type == 'code' ? 'primary' : ($content->content_type == 'error' ? 'danger' : 'secondary') }}">
                                        {{ ucfirst($content->content_type) }}
                                    </span>
                                </td>
                                <td>{{ $content->section->title ?? 'N/A' }}</td>
                                <td>{{ $content->section->category->name ?? 'N/A' }}</td>
                                <td>{{ $content->sort_order }}</td>
                                <td>
                                    @if($content->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url('/admin/documentation/contents/' . $content->id . '/edit') }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ url('/admin/documentation/contents/' . $content->id) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No content found</h5>
                <p class="text-muted">Start by creating your first content item.</p>
                <a href="{{ url('/admin/documentation/contents/create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create First Content
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
