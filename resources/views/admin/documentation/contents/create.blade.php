@extends('admin.layouts.app')

@section('title', 'Create Content')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-plus"></i> Create New Content
                </h1>
                <div class="page-actions">
                    <a href="{{ route('admin.documentation.contents') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Contents
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Content Details</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.documentation.contents.store') }}" method="POST" id="contentForm">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="section_id" class="form-label">Section *</label>
                                    <select name="section_id" id="section_id" class="form-control" required>
                                        <option value="">Select Section</option>
                                        @foreach($sections as $section)
                                            <option value="{{ $section->id }}" {{ old('section_id') == $section->id ? 'selected' : '' }}>
                                                {{ $section->category->name }} - {{ $section->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('section_id'))
                                        <div class="text-danger">{{ $errors->first('section_id') }}</div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="content_type" class="form-label">Content Type *</label>
                                    <select name="content_type" id="content_type" class="form-control" required>
                                        <option value="">Select Type</option>
                                        @foreach($contentTypes as $key => $label)
                                            <option value="{{ $key }}" {{ old('content_type') == $key ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('content_type'))
                                        <div class="text-danger">{{ $errors->first('content_type') }}</div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="title" class="form-label">Title *</label>
                            <input type="text" name="title" id="title" class="form-control" value="{{ old('title') }}" required>
                            @if($errors->has('title'))
                                <div class="text-danger">{{ $errors->first('title') }}</div>
                            @endif
                        </div>

                        <div class="form-group mb-3" id="language_group" style="display: none;">
                            <label for="language" class="form-label">Programming Language</label>
                            <select name="language" id="language" class="form-control">
                                <option value="">Select Language</option>
                                <option value="php">PHP</option>
                                <option value="javascript">JavaScript</option>
                                <option value="bash">Bash/Shell</option>
                                <option value="sql">SQL</option>
                                <option value="json">JSON</option>
                                <option value="html">HTML</option>
                                <option value="css">CSS</option>
                                <option value="env">Environment</option>
                                <option value="text">Plain Text</option>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="content" class="form-label">Content *</label>
                            <textarea name="content" id="content" class="form-control" rows="15" required>{{ old('content') }}</textarea>
                            @if($errors->has('content'))
                                <div class="text-danger">{{ $errors->first('content') }}</div>
                            @endif
                            <small class="form-text text-muted" id="content_help">
                                Enter your content here. The format will depend on the content type selected.
                            </small>
                        </div>

                        <div class="form-group mb-3" id="metadata_group" style="display: none;">
                            <label for="metadata" class="form-label">Metadata (JSON)</label>
                            <textarea name="metadata" id="metadata" class="form-control" rows="5">{{ old('metadata') }}</textarea>
                            @if($errors->has('metadata'))
                                <div class="text-danger">{{ $errors->first('metadata') }}</div>
                            @endif
                            <small class="form-text text-muted">
                                For tables: {"headers": ["Col1", "Col2"], "rows": [["Data1", "Data2"]]}
                            </small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" name="sort_order" id="sort_order" class="form-control" value="{{ old('sort_order', 0) }}" min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <div class="form-check mt-4">
                                        <input type="checkbox" name="is_active" id="is_active" class="form-check-input" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label for="is_active" class="form-check-label">Active</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Content
                            </button>
                            <a href="{{ route('admin.documentation.contents') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Content Type Guide</h5>
                </div>
                <div class="card-body">
                    <div class="content-type-help">
                        <div class="help-item" data-type="text">
                            <h6><i class="fas fa-file-alt"></i> Text Content</h6>
                            <p>Regular text content with basic formatting. Supports line breaks.</p>
                        </div>
                        
                        <div class="help-item" data-type="code">
                            <h6><i class="fas fa-code"></i> Code Block</h6>
                            <p>Syntax-highlighted code. Select the programming language for proper highlighting.</p>
                        </div>
                        
                        <div class="help-item" data-type="command">
                            <h6><i class="fas fa-terminal"></i> Command</h6>
                            <p>Terminal/shell commands. Will be displayed with copy functionality.</p>
                        </div>
                        
                        <div class="help-item" data-type="response">
                            <h6><i class="fas fa-reply"></i> Response</h6>
                            <p>API responses or command outputs. Usually JSON or text responses.</p>
                        </div>
                        
                        <div class="help-item" data-type="error">
                            <h6><i class="fas fa-exclamation-triangle"></i> Error</h6>
                            <p>Error messages or warnings. Will be displayed with error styling.</p>
                        </div>
                        
                        <div class="help-item" data-type="table">
                            <h6><i class="fas fa-table"></i> Table</h6>
                            <p>Tabular data. Use metadata field to define headers and rows in JSON format.</p>
                        </div>
                        
                        <div class="help-item" data-type="list">
                            <h6><i class="fas fa-list"></i> List</h6>
                            <p>Bullet point lists. Each line will become a list item.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">Preview</h5>
                </div>
                <div class="card-body">
                    <div id="content_preview">
                        <p class="text-muted">Select content type and enter content to see preview.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const contentTypeSelect = document.getElementById('content_type');
    const languageGroup = document.getElementById('language_group');
    const metadataGroup = document.getElementById('metadata_group');
    const contentHelp = document.getElementById('content_help');
    const contentTextarea = document.getElementById('content');
    const preview = document.getElementById('content_preview');

    // Content type change handler
    contentTypeSelect.addEventListener('change', function() {
        const type = this.value;
        
        // Show/hide language field for code blocks
        if (type === 'code') {
            languageGroup.style.display = 'block';
        } else {
            languageGroup.style.display = 'none';
        }
        
        // Show/hide metadata field for tables
        if (type === 'table') {
            metadataGroup.style.display = 'block';
        } else {
            metadataGroup.style.display = 'none';
        }
        
        // Update help text
        updateHelpText(type);
        
        // Update preview
        updatePreview();
    });

    // Content change handler for preview
    contentTextarea.addEventListener('input', updatePreview);

    function updateHelpText(type) {
        const helpTexts = {
            'text': 'Enter regular text content. Line breaks will be preserved.',
            'code': 'Enter code content. Select the programming language for syntax highlighting.',
            'command': 'Enter shell/terminal commands. Each line should be a separate command.',
            'response': 'Enter API responses or command outputs. Usually JSON or plain text.',
            'error': 'Enter error messages or warnings.',
            'warning': 'Enter warning messages.',
            'info': 'Enter informational content.',
            'table': 'Content field not used for tables. Use metadata field to define table structure.',
            'list': 'Enter list items, one per line.'
        };
        
        contentHelp.textContent = helpTexts[type] || 'Enter your content here.';
    }

    function updatePreview() {
        const type = contentTypeSelect.value;
        const content = contentTextarea.value;
        
        if (!type || !content) {
            preview.innerHTML = '<p class="text-muted">Select content type and enter content to see preview.</p>';
            return;
        }
        
        // Simple preview generation
        let previewHtml = '';
        
        switch (type) {
            case 'text':
                previewHtml = '<div class="text-content">' + content.replace(/\n/g, '<br>') + '</div>';
                break;
            case 'code':
                previewHtml = '<div class="code-block"><pre><code>' + escapeHtml(content) + '</code></pre></div>';
                break;
            case 'command':
                previewHtml = '<div class="command">' + escapeHtml(content) + '</div>';
                break;
            case 'response':
                previewHtml = '<div class="response-block">' + escapeHtml(content) + '</div>';
                break;
            case 'error':
                previewHtml = '<div class="error-block">' + escapeHtml(content) + '</div>';
                break;
            case 'list':
                const items = content.split('\n').filter(item => item.trim());
                previewHtml = '<ul>' + items.map(item => '<li>' + escapeHtml(item.trim()) + '</li>').join('') + '</ul>';
                break;
            default:
                previewHtml = '<div>' + escapeHtml(content) + '</div>';
        }
        
        preview.innerHTML = previewHtml;
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
});
</script>

<style>
.help-item {
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-left: 3px solid #007bff;
    background: #f8f9fa;
}

.help-item h6 {
    margin-bottom: 0.5rem;
    color: #007bff;
}

.help-item p {
    margin: 0;
    font-size: 0.9rem;
    color: #666;
}

.form-actions {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

#content_preview {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    padding: 1rem;
    border-radius: 5px;
    background: #f8f9fa;
}

.code-block {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 5px;
    font-family: monospace;
}

.command {
    background: #2d3748;
    color: #68d391;
    padding: 1rem;
    border-radius: 5px;
    font-family: monospace;
}

.response-block {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-left: 4px solid #48bb78;
    padding: 1rem;
    border-radius: 5px;
}

.error-block {
    background: #fed7d7;
    border: 1px solid #fc8181;
    border-left: 4px solid #e53e3e;
    padding: 1rem;
    border-radius: 5px;
}
</style>
@endsection
