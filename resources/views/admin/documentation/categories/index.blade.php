@extends('admin.layouts.app')

@section('title', 'Categories')

@section('content')
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-folder"></i> Documentation Categories
    </h1>
    <div class="page-actions">
        <a href="{{ url('/admin/documentation/categories/create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Category
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">All Categories</h5>
    </div>
    <div class="card-body">
        @if(isset($categories) && $categories->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Icon</th>
                            <th>Sections</th>
                            <th>Sort Order</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($categories as $category)
                            <tr>
                                <td>
                                    <strong>{{ $category->name }}</strong>
                                    @if($category->description)
                                        <br><small class="text-muted">{{ str_limit($category->description, 50) }}</small>
                                    @endif
                                </td>
                                <td>
                                    @if($category->icon)
                                        <i class="{{ $category->icon }}"></i>
                                    @else
                                        <span class="text-muted">No icon</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $category->sections->count() }} sections</span>
                                </td>
                                <td>{{ $category->sort_order }}</td>
                                <td>
                                    @if($category->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url('/admin/documentation/categories/' . $category->id . '/edit') }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ url('/admin/documentation/categories/' . $category->id) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-folder fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No categories found</h5>
                <p class="text-muted">Start by creating your first documentation category.</p>
                <a href="{{ url('/admin/documentation/categories/create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create First Category
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
