# Laravel 5.6 + Redis Queue + Supervisor + CleverTap Integration

## 🎯 Complete Setup Documentation

This document provides a comprehensive guide for setting up Laravel 5.6 with Redis Queue, Supervisor, and CleverTap integration for asynchronous event processing.

## 📋 Prerequisites

- PHP 7.4+
- MySQL Database
- Redis Server
- Supervisor
- Composer

## 🔧 Environment Configuration

### 1. Database Configuration (.env)

```env
# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=redisQueueConnectionDatabase
DB_USERNAME=root
DB_PASSWORD=rgdatabase@2024

# Queue Configuration
QUEUE_CONNECTION=redis

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Application Configuration
APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:YOUR_APP_KEY_HERE
APP_DEBUG=true
APP_URL=http://localhost:8000

# Cache Configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis
```

### 2. Queue Configuration (config/queue.php)

```php
'connections' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => env('REDIS_QUEUE', 'default'),
        'retry_after' => 90,
        'block_for' => null,
    ],
],
```

## 🗄️ Database Schema

### Required Tables

1. **clevertap_events** - Tracks all CleverTap events
2. **settings** - Stores CleverTap configuration
3. **failed_jobs** - Laravel failed jobs table
4. **migrations** - Laravel migrations tracking

### CleverTap Events Table Structure

```sql
CREATE TABLE `clevertap_events` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_identity` varchar(255) NOT NULL,
  `event_name` varchar(255) NOT NULL,
  `event_data` longtext,
  `message` text,
  `status` enum('pending','sent','failed') DEFAULT 'pending',
  `response_data` longtext,
  `job_id` varchar(255),
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_identity` (`user_identity`),
  KEY `idx_event_name` (`event_name`),
  KEY `idx_status` (`status`)
);
```

### Settings Table Structure

```sql
CREATE TABLE `setting` (
  `id` int NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(30) NOT NULL,
  `value` text NOT NULL,
  `enable_auto_bank_kyc` varchar(200) DEFAULT NULL,
  `category_cache` int NOT NULL DEFAULT '1',
  `teams_cache` int NOT NULL DEFAULT '1',
  `teams_write_cache` int NOT NULL DEFAULT '1',
  `lineup_cache` int NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `setting_key` (`setting_key`)
);
```

## ⚙️ Supervisor Configuration

### 1. Create Supervisor Config File

```bash
sudo nano /etc/supervisor/conf.d/laravel-queue-worker.conf
```

### 2. Supervisor Configuration Content

```ini
[program:laravel-queue-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /home/<USER>/Documents/development/my document/Laravel/visoion11/forErroFixing/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
directory=/home/<USER>/Documents/development/my document/Laravel/visoion11/forErroFixing
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=kapil
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/laravel-queue-worker.log
stopwaitsecs=3600
```

### 3. Supervisor Management Commands

```bash
# Update supervisor configuration
sudo supervisorctl reread
sudo supervisorctl update

# Start workers
sudo supervisorctl start laravel-queue-worker:*

# Check status
sudo supervisorctl status

# Restart workers
sudo supervisorctl restart laravel-queue-worker:*

# Stop workers
sudo supervisorctl stop laravel-queue-worker:*
```

## 🔌 CleverTap Configuration

### 1. Settings Database Entry

Insert CleverTap configuration in the `setting` table:

```sql
INSERT INTO `setting` (`setting_key`, `value`) VALUES 
('CLEVERTAP_DEV_KEY', '{"project_id":"TEST-549-894-847Z","project_token":"TEST-4cb-c45","passcode":"YAS-KUA-CAEL","region":"in1"}');
```

### 2. Analytics Events Configuration

Create `config/analytics_events.php`:

```php
<?php
return [
    'auth/getplayerlist' => [
        'event' => 'player_list_viewed',
        'message' => 'Player List Viewed'
    ],
    // Add more endpoint configurations here
];
```

## 🚀 API Endpoints

### 1. Test CleverTap Queue Integration

```bash
curl -X POST "http://localhost:8000/api/clevertap/test-queue" \
  -H "Content-Type: application/json" \
  -d '{"user_id":"12501981","event":"test_event","message":"Test message"}'
```

### 2. View Events History

```bash
curl "http://localhost:8000/api/clevertap/events-history"
```

### 3. Update CleverTap Settings

```bash
curl -X POST "http://localhost:8000/api/clevertap/update-settings" \
  -H "Content-Type: application/json" \
  -d '{"project_id":"YOUR_PROJECT_ID","passcode":"YOUR_PASSCODE","region":"in1"}'
```

### 4. Test Existing Middleware

```bash
curl -X POST "http://localhost:8000/api/auth/getplayerlist" \
  -H "Content-Type: application/json" \
  -d '{"user_id":"12501981","matchkey":"89823","sport_key":"CRICKET"}'
```

## 📊 Monitoring and Debugging

### 1. Monitor Queue Workers

```bash
# Check supervisor status
sudo supervisorctl status

# Monitor queue logs
tail -f /var/log/laravel-queue-worker.log

# Check Laravel logs
tail -f storage/logs/laravel.log
```

### 2. Redis Monitoring

```bash
# Check Redis connection
redis-cli ping

# Monitor Redis queues
redis-cli LLEN queues:default

# View queue contents
redis-cli LRANGE queues:default 0 -1
```

### 3. Database Monitoring

```bash
# Check latest events
php -r "
\$pdo = new PDO('mysql:host=127.0.0.1;dbname=redisQueueConnectionDatabase', 'root', 'rgdatabase@2024');
\$stmt = \$pdo->query('SELECT * FROM clevertap_events ORDER BY created_at DESC LIMIT 5');
while (\$row = \$stmt->fetch()) {
    echo 'ID: ' . \$row['id'] . ', Event: ' . \$row['event_name'] . ', Status: ' . \$row['status'] . PHP_EOL;
}
"
```

## 🔄 Process Flow

### 1. Middleware Process Flow

1. **API Request** → Middleware intercepts request
2. **Event Detection** → Checks `analytics_events.php` configuration
3. **Database Insert** → Creates record in `clevertap_events` table
4. **Job Dispatch** → Dispatches `LogAnalyticsEventJob` to Redis queue
5. **Response** → Returns API response to client

### 2. Queue Job Process Flow

1. **Job Pickup** → Supervisor worker picks up job from Redis
2. **CleverTap API Call** → Sends event data to CleverTap
3. **Database Update** → Updates record status and response data
4. **Logging** → Logs success/failure information

## 🛠️ Key Components

### 1. Middleware
- **File**: `app/Http/Middleware/AnalyticsEventLoggerMiddleware.php`
- **Purpose**: Intercepts API requests and triggers CleverTap events
- **Registration**: Added to `app/Http/Kernel.php`

### 2. Queue Job
- **File**: `app/Jobs/LogAnalyticsEventJob.php`
- **Purpose**: Processes CleverTap API calls asynchronously
- **Queue**: Redis

### 3. CleverTap Helper
- **File**: `app/Helpers/CleverTapHelper.php`
- **Purpose**: Handles CleverTap API communication
- **Features**: Configuration management, API calls, error handling

### 4. API Controller
- **File**: `app/Http/Controllers/Api/CleverTapTestController.php`
- **Purpose**: Provides testing and management endpoints
- **Routes**: Defined in `routes/api.php`

### 5. Event Transformer
- **File**: `app/Transformers/AnalyticsEventTransformer.php`
- **Purpose**: Transforms request data for CleverTap events

## 🔍 Troubleshooting

### Common Issues and Solutions

1. **Queue Jobs Not Processing**
   ```bash
   # Check supervisor status
   sudo supervisorctl status
   
   # Restart workers
   sudo supervisorctl restart laravel-queue-worker:*
   
   # Clear cache
   php artisan config:clear && php artisan cache:clear
   ```

2. **Database Connection Issues**
   ```bash
   # Test database connection
   php artisan tinker
   DB::connection()->getPdo();
   ```

3. **Redis Connection Issues**
   ```bash
   # Test Redis connection
   redis-cli ping
   
   # Check Redis configuration
   php artisan tinker
   Redis::ping();
   ```

4. **CleverTap API Issues**
   - Verify credentials in settings table
   - Check CleverTap API response in logs
   - Ensure proper event data format

## 📈 Performance Optimization

### 1. Queue Workers
- Adjust `numprocs` in supervisor config based on load
- Monitor memory usage and restart workers periodically
- Use `--max-time` and `--max-jobs` parameters

### 2. Database
- Add indexes on frequently queried columns
- Archive old event records periodically
- Monitor database performance

### 3. Redis
- Configure Redis memory limits
- Use Redis persistence if needed
- Monitor Redis memory usage

## 🔒 Security Considerations

1. **Environment Variables**
   - Keep `.env` file secure
   - Use strong database passwords
   - Protect CleverTap credentials

2. **API Endpoints**
   - Implement proper authentication
   - Add rate limiting
   - Validate input data

3. **Queue Security**
   - Secure Redis instance
   - Use Redis AUTH if needed
   - Monitor queue access

## 📝 Maintenance Tasks

### Daily
- Monitor queue worker status
- Check error logs
- Verify CleverTap event delivery

### Weekly
- Review failed jobs
- Clean up old log files
- Monitor database growth

### Monthly
- Archive old event records
- Review and optimize configurations
- Update dependencies if needed

---

## 🎉 Success Indicators

Your integration is working correctly when you see:

1. ✅ **Supervisor Status**: All workers running
2. ✅ **Queue Processing**: Jobs being processed successfully
3. ✅ **Database Records**: Events being created and updated
4. ✅ **CleverTap Delivery**: Events reaching CleverTap with success status
5. ✅ **API Responses**: Proper responses from all endpoints
6. ✅ **Middleware Triggering**: Automatic event creation on API calls

**Your Laravel 5.6 + Redis Queue + Supervisor + CleverTap integration is now fully operational!** 🚀

## 📁 File Structure

```
project-root/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   └── Api/
│   │   │       └── CleverTapTestController.php
│   │   ├── Middleware/
│   │   │   └── AnalyticsEventLoggerMiddleware.php
│   │   └── Kernel.php
│   ├── Jobs/
│   │   └── LogAnalyticsEventJob.php
│   ├── Helpers/
│   │   └── CleverTapHelper.php
│   ├── Services/
│   │   └── CurlService.php
│   └── Transformers/
│       └── AnalyticsEventTransformer.php
├── config/
│   ├── analytics_events.php
│   ├── queue.php
│   └── database.php
├── database/
│   └── migrations/
│       ├── create_clevertap_events_table.php
│       └── create_settings_table.php
├── routes/
│   └── api.php
├── .env
└── CLEVERTAP_INTEGRATION_DOCUMENTATION.md
```

## 🧪 Testing Scenarios

### 1. Basic Queue Test
```bash
# Test basic queue functionality
curl -X POST "http://localhost:8000/api/clevertap/test-queue" \
  -H "Content-Type: application/json" \
  -d '{"user_id":"test123","event":"test_event","message":"Basic test"}'

# Expected: Event created with status "sent"
```

### 2. Middleware Integration Test
```bash
# Test middleware automatic triggering
curl -X POST "http://localhost:8000/api/auth/getplayerlist" \
  -H "Content-Type: application/json" \
  -d '{"user_id":"test123","matchkey":"89823","sport_key":"CRICKET"}'

# Expected: "player_list_viewed" event automatically created
```

### 3. Settings Update Test
```bash
# Test CleverTap settings update
curl -X POST "http://localhost:8000/api/clevertap/update-settings" \
  -H "Content-Type: application/json" \
  -d '{"project_id":"NEW_PROJECT_ID","passcode":"NEW_PASSCODE","region":"in1"}'

# Expected: Settings updated successfully
```

### 4. Events History Test
```bash
# Test events retrieval
curl "http://localhost:8000/api/clevertap/events-history?limit=5"

# Expected: List of recent events with status information
```

## 🔧 Configuration Examples

### 1. Adding New Event Endpoints

To add a new endpoint for CleverTap tracking, update `config/analytics_events.php`:

```php
<?php
return [
    'auth/getplayerlist' => [
        'event' => 'player_list_viewed',
        'message' => 'Player List Viewed'
    ],
    'auth/joincontest' => [
        'event' => 'contest_joined',
        'message' => 'User Joined Contest'
    ],
    'payment/process' => [
        'event' => 'payment_initiated',
        'message' => 'Payment Process Started'
    ],
    // Add more endpoints as needed
];
```

### 2. Custom Event Data Transformation

Update `app/Transformers/AnalyticsEventTransformer.php` to customize event data:

```php
public static function getPayload($path, $request, $responseData = [])
{
    $customData = [];

    switch ($path) {
        case 'auth/getplayerlist':
            $customData = [
                'sport_key' => $request->input('sport_key'),
                'match_key' => $request->input('matchkey'),
                'user_type' => 'player',
                'timestamp' => now()->toISOString()
            ];
            break;

        case 'payment/process':
            $customData = [
                'amount' => $request->input('amount'),
                'currency' => $request->input('currency', 'INR'),
                'payment_method' => $request->input('method'),
                'timestamp' => now()->toISOString()
            ];
            break;

        default:
            $customData = [
                'endpoint' => $path,
                'timestamp' => now()->toISOString()
            ];
    }

    return $customData;
}
```

## 🚨 Error Handling

### 1. Job Failure Handling

The system includes comprehensive error handling:

- **Automatic Retries**: Jobs retry up to 3 times
- **Failed Job Logging**: Failed jobs are logged to `failed_jobs` table
- **Status Tracking**: Database records track success/failure status
- **Error Logging**: Detailed error logs in Laravel logs

### 2. CleverTap API Error Responses

Common CleverTap API errors and solutions:

```json
// Error: Empty event data
{
  "status": "fail",
  "code": 555,
  "error": "Error",
  "record": {"evtData": []}
}
// Solution: Ensure event data is not empty

// Error: Invalid credentials
{
  "status": "fail",
  "code": 401,
  "error": "Unauthorized"
}
// Solution: Check project_id and passcode in settings

// Error: Invalid region
{
  "status": "fail",
  "code": 400,
  "error": "Bad Request"
}
// Solution: Verify region setting (in1, us1, sg1, etc.)
```

## 📊 Monitoring Dashboard

### 1. Queue Status Check

Create a simple monitoring script:

```php
<?php
// monitor.php
$redis = new Redis();
$redis->connect('127.0.0.1', 6379);

echo "Queue Status:\n";
echo "- Pending Jobs: " . $redis->lLen('queues:default') . "\n";
echo "- Failed Jobs: " . $redis->lLen('queues:default:failed') . "\n";

$pdo = new PDO('mysql:host=127.0.0.1;dbname=redisQueueConnectionDatabase', 'root', 'rgdatabase@2024');

$stmt = $pdo->query("SELECT status, COUNT(*) as count FROM clevertap_events GROUP BY status");
echo "\nEvent Status:\n";
while ($row = $stmt->fetch()) {
    echo "- {$row['status']}: {$row['count']}\n";
}
```

### 2. Health Check Endpoint

Add to your API routes:

```php
Route::get('/health/clevertap', function() {
    $health = [
        'redis' => Redis::ping() === '+PONG',
        'database' => DB::connection()->getPdo() !== null,
        'queue_workers' => exec('sudo supervisorctl status laravel-queue-worker:* | grep RUNNING | wc -l'),
        'recent_events' => DB::table('clevertap_events')->where('created_at', '>', now()->subHour())->count()
    ];

    return response()->json($health);
});
```

## 🔄 Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] Redis server running
- [ ] Supervisor configuration updated
- [ ] CleverTap credentials verified

### Post-Deployment
- [ ] Queue workers started
- [ ] Test API endpoints
- [ ] Verify CleverTap integration
- [ ] Monitor logs for errors
- [ ] Check database records

### Production Considerations
- [ ] Set up log rotation
- [ ] Configure monitoring alerts
- [ ] Implement backup strategy
- [ ] Set up SSL certificates
- [ ] Configure firewall rules

---

## 📞 Support and Maintenance

### Log Files Locations
- **Laravel Logs**: `storage/logs/laravel.log`
- **Queue Worker Logs**: `/var/log/laravel-queue-worker.log`
- **Supervisor Logs**: `/var/log/supervisor/`

### Useful Commands
```bash
# Clear all caches
php artisan config:clear && php artisan cache:clear && php artisan route:clear

# Restart queue workers
sudo supervisorctl restart laravel-queue-worker:*

# Check failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all

# Monitor queue in real-time
watch -n 1 'redis-cli LLEN queues:default'
```

This documentation provides everything needed to understand, maintain, and troubleshoot your Laravel 5.6 + Redis Queue + Supervisor + CleverTap integration! 🎯
