<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CleverTapSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('settings')->updateOrInsert(
            ['setting_key' => 'CLEVERTAP_DEV_KEY'],
            [
                'value' => json_encode([
                    'project_id' => 'YOUR_CLEVERTAP_PROJECT_ID',
                    'passcode' => 'YOUR_CLEVERTAP_PASSCODE',
                    'region' => 'in1'
                ]),
                'description' => 'CleverTap API Configuration',
                'created_at' => now(),
                'updated_at' => now()
            ]
        );
    }
}
