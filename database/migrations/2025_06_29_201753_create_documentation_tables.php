<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDocumentationTables extends Migration
{
    public function up()
    {
        // Documentation Categories Table
        Schema::create('doc_categories', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('icon')->nullable();
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Documentation Sections Table
        Schema::create('doc_sections', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('category_id');
            $table->string('title');
            $table->string('slug')->unique();
            $table->string('icon')->nullable();
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('category_id')->references('id')->on('doc_categories')->onDelete('cascade');
            $table->index(['category_id', 'sort_order']);
        });

        // Documentation Content Table
        Schema::create('doc_contents', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('section_id');
            $table->string('title');
            $table->string('slug')->unique();
            $table->enum('content_type', ['text', 'code', 'command', 'response', 'error', 'warning', 'info', 'table', 'list']);
            $table->longText('content');
            $table->string('language')->nullable(); // For code blocks
            $table->json('metadata')->nullable(); // For additional data like table headers, etc.
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('section_id')->references('id')->on('doc_sections')->onDelete('cascade');
            $table->index(['section_id', 'sort_order']);
        });

        // Documentation Templates Table
        Schema::create('doc_templates', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->longText('html_template');
            $table->longText('css_styles')->nullable();
            $table->longText('js_scripts')->nullable();
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Documentation Settings Table
        Schema::create('doc_settings', function (Blueprint $table) {
            $table->increments('id');
            $table->string('key')->unique();
            $table->longText('value');
            $table->string('type')->default('text'); // text, json, boolean, number
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('doc_contents');
        Schema::dropIfExists('doc_sections');
        Schema::dropIfExists('doc_categories');
        Schema::dropIfExists('doc_templates');
        Schema::dropIfExists('doc_settings');
    }
}
