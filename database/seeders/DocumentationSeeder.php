<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DocCategory;
use App\Models\DocSection;
use App\Models\DocContent;
use App\Models\DocTemplate;

class DocumentationSeeder extends Seeder
{
    public function run()
    {
        // Create default template
        $this->createDefaultTemplate();
        
        // Create Laravel + Redis + CleverTap Documentation
        $this->createLaravelDocumentation();
    }

    private function createDefaultTemplate()
    {
        DocTemplate::create([
            'name' => 'Default Documentation Template',
            'slug' => 'default-template',
            'description' => 'Default template with sidebar navigation and responsive design',
            'html_template' => $this->getDefaultHtmlTemplate(),
            'css_styles' => $this->getDefaultCssStyles(),
            'js_scripts' => $this->getDefaultJsScripts(),
            'is_default' => true,
            'is_active' => true
        ]);
    }

    private function createLaravelDocumentation()
    {
        // 1. Project Overview
        $overview = DocCategory::create([
            'name' => 'Project Overview',
            'slug' => 'project-overview',
            'icon' => 'fas fa-info-circle',
            'description' => 'Complete overview of the Laravel 5.6 + Redis Queue + CleverTap integration project',
            'sort_order' => 1
        ]);

        $overviewSection = DocSection::create([
            'category_id' => $overview->id,
            'title' => 'Project Overview',
            'slug' => 'overview',
            'icon' => 'fas fa-info-circle',
            'description' => 'What we built and key features implemented',
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $overviewSection->id,
            'title' => 'What We Built',
            'slug' => 'what-we-built',
            'content_type' => 'info',
            'content' => 'A complete Laravel 5.6 application with Redis Queue processing and CleverTap integration for asynchronous event tracking.',
            'sort_order' => 1
        ]);

        // 2. Prerequisites
        $prerequisites = DocCategory::create([
            'name' => 'Prerequisites',
            'slug' => 'prerequisites',
            'icon' => 'fas fa-list-check',
            'description' => 'Required software and system requirements',
            'sort_order' => 2
        ]);

        $prereqSection = DocSection::create([
            'category_id' => $prerequisites->id,
            'title' => 'System Requirements',
            'slug' => 'system-requirements',
            'icon' => 'fas fa-server',
            'description' => 'Software and versions required for the setup',
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $prereqSection->id,
            'title' => 'Required Software',
            'slug' => 'required-software',
            'content_type' => 'table',
            'content' => '',
            'metadata' => [
                'headers' => ['Software', 'Version', 'Installation Command', 'Verification'],
                'rows' => [
                    ['PHP', '7.4+', 'sudo apt-get install php7.4', 'php --version'],
                    ['MySQL', '5.7+', 'sudo apt-get install mysql-server', 'mysql --version'],
                    ['Redis', '5.0+', 'sudo apt-get install redis-server', 'redis-cli ping'],
                    ['Supervisor', 'Latest', 'sudo apt-get install supervisor', 'supervisorctl --version'],
                    ['Composer', '2.0+', 'curl -sS https://getcomposer.org/installer | php', 'composer --version']
                ]
            ],
            'sort_order' => 1
        ]);

        // 3. Environment Setup
        $environment = DocCategory::create([
            'name' => 'Environment Setup',
            'slug' => 'environment-setup',
            'icon' => 'fas fa-cog',
            'description' => 'Complete environment configuration and dependencies',
            'sort_order' => 3
        ]);

        $envSection = DocSection::create([
            'category_id' => $environment->id,
            'title' => 'Environment Configuration',
            'slug' => 'environment-configuration',
            'icon' => 'fas fa-cog',
            'description' => 'Configure .env file and install dependencies',
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $envSection->id,
            'title' => 'Configure Environment Variables (.env)',
            'slug' => 'configure-env',
            'content_type' => 'code',
            'content' => "# Application Configuration\nAPP_NAME=Laravel\nAPP_ENV=local\nAPP_KEY=base64:YOUR_APP_KEY_HERE\nAPP_DEBUG=true\nAPP_URL=http://localhost:8000\n\n# Database Configuration\nDB_CONNECTION=mysql\nDB_HOST=127.0.0.1\nDB_PORT=3306\nDB_DATABASE=redisQueueConnectionDatabase\nDB_USERNAME=root\nDB_PASSWORD=rgdatabase@2024\n\n# Queue Configuration\nQUEUE_CONNECTION=redis\n\n# Redis Configuration\nREDIS_HOST=127.0.0.1\nREDIS_PASSWORD=null\nREDIS_PORT=6379\n\n# Cache Configuration\nCACHE_DRIVER=redis\nSESSION_DRIVER=redis",
            'language' => 'env',
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $envSection->id,
            'title' => 'Install Dependencies',
            'slug' => 'install-dependencies',
            'content_type' => 'command',
            'content' => "# Install Laravel dependencies\ncomposer install\n\n# Install Redis PHP extension\nsudo apt-get install php-redis\n\n# Install Supervisor\nsudo apt-get install supervisor\n\n# Generate application key\nphp artisan key:generate",
            'sort_order' => 2
        ]);

        // 4. Database Setup
        $database = DocCategory::create([
            'name' => 'Database Setup',
            'slug' => 'database-setup',
            'icon' => 'fas fa-database',
            'description' => 'Database creation, migrations, and configuration',
            'sort_order' => 4
        ]);

        $dbSection = DocSection::create([
            'category_id' => $database->id,
            'title' => 'Database Configuration',
            'slug' => 'database-configuration',
            'icon' => 'fas fa-database',
            'description' => 'Create database and run migrations',
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $dbSection->id,
            'title' => 'Create Database',
            'slug' => 'create-database',
            'content_type' => 'command',
            'content' => "# Connect to MySQL and create database\nmysql -u root -p\n\nCREATE DATABASE redisQueueConnectionDatabase;\nUSE redisQueueConnectionDatabase;\nexit;",
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $dbSection->id,
            'title' => 'Run Migrations',
            'slug' => 'run-migrations',
            'content_type' => 'command',
            'content' => "# Run the migrations\nphp artisan migrate\n\n# Verify tables were created\nphp -r \"\n\$pdo = new PDO('mysql:host=127.0.0.1;dbname=redisQueueConnectionDatabase', 'root', 'rgdatabase@2024');\n\$stmt = \$pdo->query('SHOW TABLES');\necho 'Tables created:' . PHP_EOL;\nwhile (\$row = \$stmt->fetch()) {\n    echo '- ' . \$row[0] . PHP_EOL;\n}\n\"",
            'sort_order' => 2
        ]);

        // 5. Testing & Verification
        $testing = DocCategory::create([
            'name' => 'Testing & Verification',
            'slug' => 'testing-verification',
            'icon' => 'fas fa-vial',
            'description' => 'Complete testing procedures with real API calls',
            'sort_order' => 5
        ]);

        $testSection = DocSection::create([
            'category_id' => $testing->id,
            'title' => 'API Testing',
            'slug' => 'api-testing',
            'icon' => 'fas fa-vial',
            'description' => 'Test the complete integration with curl commands',
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $testSection->id,
            'title' => 'Start Laravel Server',
            'slug' => 'start-server',
            'content_type' => 'command',
            'content' => "# Start the Laravel server\nphp artisan serve --host=0.0.0.0 --port=8000",
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $testSection->id,
            'title' => 'Test CleverTap Integration',
            'slug' => 'test-clevertap',
            'content_type' => 'command',
            'content' => 'curl -X POST "http://localhost:8000/api/clevertap/test-queue" \\' . "\n" . '  -H "Content-Type: application/json" \\' . "\n" . '  -d \'{\n    "user_id": "12501981",\n    "event": "test_api_event",\n    "message": "Testing CleverTap integration from API"\n  }\'',
            'sort_order' => 2
        ]);

        DocContent::create([
            'section_id' => $testSection->id,
            'title' => 'Expected Response',
            'slug' => 'expected-response',
            'content_type' => 'response',
            'content' => '{\n  "success": true,\n  "message": "CleverTap event dispatched to queue successfully",\n  "data": {\n    "event_id": 6,\n    "user_identity": "12501981",\n    "event_name": "test_api_event",\n    "queue_status": "dispatched"\n  }\n}',
            'sort_order' => 3
        ]);

        // 6. Troubleshooting
        $troubleshooting = DocCategory::create([
            'name' => 'Troubleshooting',
            'slug' => 'troubleshooting',
            'icon' => 'fas fa-wrench',
            'description' => 'Common issues and their solutions',
            'sort_order' => 6
        ]);

        $troubleSection = DocSection::create([
            'category_id' => $troubleshooting->id,
            'title' => 'Common Issues',
            'slug' => 'common-issues',
            'icon' => 'fas fa-exclamation-triangle',
            'description' => 'Real errors encountered and their solutions',
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $troubleSection->id,
            'title' => 'Database Connection Issues',
            'slug' => 'database-connection-issues',
            'content_type' => 'error',
            'content' => 'SQLSTATE[HY000] [1045] Access denied for user \'your_username\'@\'localhost\'',
            'sort_order' => 1
        ]);

        DocContent::create([
            'section_id' => $troubleSection->id,
            'title' => 'Solution',
            'slug' => 'database-solution',
            'content_type' => 'command',
            'content' => "# Clear configuration cache\nphp artisan config:clear && php artisan cache:clear\n\n# Restart supervisor workers\nsudo supervisorctl restart laravel-queue-worker:*",
            'sort_order' => 2
        ]);
    }

    private function getDefaultHtmlTemplate()
    {
        return '<!-- This would contain the HTML template structure -->';
    }

    private function getDefaultCssStyles()
    {
        return '/* Default CSS styles for documentation */';
    }

    private function getDefaultJsScripts()
    {
        return '/* Default JavaScript for documentation */';
    }
}
