# 🐘 PHP API Examples - CleverTap Integration

## 🚀 Complete PHP Implementation Examples

### 📋 Table of Contents
1. [Basic API Client Class](#basic-api-client-class)
2. [Advanced Usage Examples](#advanced-usage-examples)
3. [Batch Operations](#batch-operations)
4. [Error Handling](#error-handling)
5. [Monitoring & Analytics](#monitoring--analytics)
6. [Testing Scenarios](#testing-scenarios)

---

## 🔧 Basic API Client Class

```php
<?php
/**
 * CleverTap Laravel API Client
 * 
 * A comprehensive PHP client for interacting with the CleverTap Laravel integration
 */
class CleverTapApiClient
{
    private $baseUrl;
    private $timeout;
    private $headers;
    
    public function __construct($baseUrl = 'http://localhost:8000', $timeout = 30)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->timeout = $timeout;
        $this->headers = [
            'Content-Type: application/json',
            'Accept: application/json',
            'User-Agent: CleverTap-PHP-Client/1.0'
        ];
    }
    
    /**
     * Send event to CleverTap queue
     */
    public function sendEvent($userId, $eventName, $message = null, $customData = [])
    {
        $payload = [
            'user_id' => $userId,
            'event' => $eventName,
            'message' => $message ?: "Event: $eventName",
            'custom_data' => array_merge($customData, [
                'timestamp' => date('c'),
                'source' => 'php_client'
            ])
        ];
        
        return $this->makeRequest('POST', '/api/clevertap/test-queue', $payload);
    }
    
    /**
     * Get events history with filtering
     */
    public function getEvents($filters = [])
    {
        $defaultFilters = [
            'limit' => 50,
            'offset' => 0
        ];
        
        $params = array_merge($defaultFilters, $filters);
        $queryString = http_build_query($params);
        
        return $this->makeRequest('GET', "/api/clevertap/events-history?$queryString");
    }
    
    /**
     * Update CleverTap settings
     */
    public function updateSettings($projectId, $projectToken, $passcode, $region = 'in1')
    {
        $payload = [
            'project_id' => $projectId,
            'project_token' => $projectToken,
            'passcode' => $passcode,
            'region' => $region
        ];
        
        return $this->makeRequest('POST', '/api/clevertap/update-settings', $payload);
    }
    
    /**
     * Test middleware integration
     */
    public function testMiddleware($userId, $matchKey, $sportKey, $challengeId = '0')
    {
        $payload = [
            'user_id' => $userId,
            'matchkey' => $matchKey,
            'sport_key' => $sportKey,
            'challenge_id' => $challengeId
        ];
        
        return $this->makeRequest('POST', '/api/auth/getplayerlist', $payload);
    }
    
    /**
     * Check system health
     */
    public function healthCheck()
    {
        return $this->makeRequest('GET', '/api/health/clevertap');
    }
    
    /**
     * Make HTTP request
     */
    private function makeRequest($method, $endpoint, $payload = null)
    {
        $url = $this->baseUrl . $endpoint;
        $curl = curl_init();
        
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_HTTPHEADER => $this->headers,
            CURLOPT_CUSTOMREQUEST => $method
        ];
        
        if ($payload && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $options[CURLOPT_POSTFIELDS] = json_encode($payload);
        }
        
        curl_setopt_array($curl, $options);
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        curl_close($curl);
        
        if ($error) {
            throw new Exception("CURL Error: $error");
        }
        
        $data = json_decode($response, true);
        
        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'http_code' => $httpCode,
            'data' => $data,
            'raw_response' => $response
        ];
    }
    
    /**
     * Get events by status
     */
    public function getEventsByStatus($status, $limit = 20)
    {
        return $this->getEvents(['status' => $status, 'limit' => $limit]);
    }
    
    /**
     * Get events for specific user
     */
    public function getUserEvents($userId, $limit = 50)
    {
        return $this->getEvents(['user_id' => $userId, 'limit' => $limit]);
    }
    
    /**
     * Get recent events (last hour)
     */
    public function getRecentEvents($limit = 20)
    {
        return $this->getEvents(['limit' => $limit]);
    }
}
?>
```

---

## 🎯 Advanced Usage Examples

### 1. Complete Integration Test

```php
<?php
/**
 * Complete CleverTap Integration Test
 */
function runCompleteIntegrationTest()
{
    $client = new CleverTapApiClient();
    $testUserId = 'test_' . time();
    $results = [];
    
    echo "🧪 Starting Complete Integration Test...\n";
    echo "Test User ID: $testUserId\n\n";
    
    // Test 1: Health Check
    echo "1️⃣ Testing Health Check...\n";
    $health = $client->healthCheck();
    $results['health_check'] = $health['success'];
    echo $health['success'] ? "✅ Health check passed\n" : "❌ Health check failed\n";
    
    // Test 2: Send Custom Event
    echo "\n2️⃣ Testing Custom Event...\n";
    $eventResult = $client->sendEvent(
        $testUserId,
        'integration_test',
        'Complete integration test event',
        [
            'test_type' => 'complete_integration',
            'environment' => 'testing',
            'version' => '1.0.0'
        ]
    );
    $results['send_event'] = $eventResult['success'];
    echo $eventResult['success'] ? "✅ Event sent successfully\n" : "❌ Event sending failed\n";
    
    if ($eventResult['success']) {
        $eventId = $eventResult['data']['data']['event_id'];
        echo "Event ID: $eventId\n";
    }
    
    // Test 3: Middleware Integration
    echo "\n3️⃣ Testing Middleware Integration...\n";
    $middlewareResult = $client->testMiddleware($testUserId, '12345', 'CRICKET', '1');
    $results['middleware'] = $middlewareResult['success'];
    echo $middlewareResult['success'] ? "✅ Middleware test passed\n" : "❌ Middleware test failed\n";
    
    // Wait for queue processing
    echo "\n⏳ Waiting for queue processing (5 seconds)...\n";
    sleep(5);
    
    // Test 4: Verify Events Created
    echo "\n4️⃣ Verifying Events Created...\n";
    $userEvents = $client->getUserEvents($testUserId, 10);
    $results['verify_events'] = $userEvents['success'];
    
    if ($userEvents['success']) {
        $eventCount = count($userEvents['data']['data']);
        echo "✅ Found $eventCount events for test user\n";
        
        foreach ($userEvents['data']['data'] as $event) {
            echo "  - Event: {$event['event_name']}, Status: {$event['status']}\n";
        }
    } else {
        echo "❌ Failed to retrieve user events\n";
    }
    
    // Test 5: Check Event Status
    echo "\n5️⃣ Checking Event Processing Status...\n";
    $sentEvents = $client->getEventsByStatus('sent', 5);
    $pendingEvents = $client->getEventsByStatus('pending', 5);
    $failedEvents = $client->getEventsByStatus('failed', 5);
    
    if ($sentEvents['success']) {
        $sentCount = count($sentEvents['data']['data']);
        echo "✅ Sent events: $sentCount\n";
    }
    
    if ($pendingEvents['success']) {
        $pendingCount = count($pendingEvents['data']['data']);
        echo "⏳ Pending events: $pendingCount\n";
    }
    
    if ($failedEvents['success']) {
        $failedCount = count($failedEvents['data']['data']);
        echo $failedCount > 0 ? "⚠️ Failed events: $failedCount\n" : "✅ No failed events\n";
    }
    
    // Summary
    echo "\n📊 Test Summary:\n";
    $passedTests = array_sum($results);
    $totalTests = count($results);
    echo "Passed: $passedTests/$totalTests tests\n";
    
    foreach ($results as $test => $passed) {
        $status = $passed ? '✅' : '❌';
        echo "$status " . ucfirst(str_replace('_', ' ', $test)) . "\n";
    }
    
    return $passedTests === $totalTests;
}

// Run the test
if (runCompleteIntegrationTest()) {
    echo "\n🎉 All tests passed! Integration is working perfectly.\n";
} else {
    echo "\n⚠️ Some tests failed. Please check the system.\n";
}
?>
```

### 2. Batch Event Processing

```php
<?php
/**
 * Batch Event Processing Example
 */
function processBatchEvents($events)
{
    $client = new CleverTapApiClient();
    $results = [];
    $batchSize = 10;
    $batches = array_chunk($events, $batchSize);
    
    echo "📦 Processing " . count($events) . " events in " . count($batches) . " batches...\n";
    
    foreach ($batches as $batchIndex => $batch) {
        echo "\nProcessing batch " . ($batchIndex + 1) . "/" . count($batches) . "...\n";
        
        foreach ($batch as $eventIndex => $event) {
            try {
                $result = $client->sendEvent(
                    $event['user_id'],
                    $event['event_name'],
                    $event['message'] ?? null,
                    $event['custom_data'] ?? []
                );
                
                $results[] = [
                    'event' => $event,
                    'success' => $result['success'],
                    'response' => $result['data']
                ];
                
                echo "  ✅ Event " . ($eventIndex + 1) . ": {$event['event_name']}\n";
                
            } catch (Exception $e) {
                $results[] = [
                    'event' => $event,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                
                echo "  ❌ Event " . ($eventIndex + 1) . ": {$event['event_name']} - Error: {$e->getMessage()}\n";
            }
        }
        
        // Small delay between batches to avoid overwhelming the system
        if ($batchIndex < count($batches) - 1) {
            echo "  ⏳ Waiting 2 seconds before next batch...\n";
            sleep(2);
        }
    }
    
    // Summary
    $successful = array_filter($results, function($r) { return $r['success']; });
    $failed = array_filter($results, function($r) { return !$r['success']; });
    
    echo "\n📊 Batch Processing Summary:\n";
    echo "✅ Successful: " . count($successful) . "\n";
    echo "❌ Failed: " . count($failed) . "\n";
    echo "📈 Success Rate: " . round((count($successful) / count($results)) * 100, 2) . "%\n";
    
    return $results;
}

// Example usage
$sampleEvents = [
    [
        'user_id' => 'user_001',
        'event_name' => 'user_login',
        'message' => 'User logged in',
        'custom_data' => ['platform' => 'web', 'browser' => 'chrome']
    ],
    [
        'user_id' => 'user_002',
        'event_name' => 'purchase_completed',
        'message' => 'Purchase completed',
        'custom_data' => ['amount' => 99.99, 'currency' => 'USD', 'product_id' => 'prod_123']
    ],
    [
        'user_id' => 'user_003',
        'event_name' => 'level_completed',
        'message' => 'Level completed',
        'custom_data' => ['level' => 5, 'score' => 1500, 'time_taken' => 120]
    ]
    // Add more events as needed
];

// Process the batch
$results = processBatchEvents($sampleEvents);
?>
```

### 3. Real-time Monitoring

```php
<?php
/**
 * Real-time System Monitoring
 */
function monitorSystemRealTime($duration = 300) // 5 minutes default
{
    $client = new CleverTapApiClient();
    $startTime = time();
    $endTime = $startTime + $duration;
    $interval = 10; // Check every 10 seconds
    
    echo "🔍 Starting real-time monitoring for " . ($duration / 60) . " minutes...\n";
    echo "Checking every $interval seconds\n\n";
    
    $previousStats = null;
    
    while (time() < $endTime) {
        $currentTime = date('H:i:s');
        echo "[$currentTime] Checking system status...\n";
        
        // Health check
        $health = $client->healthCheck();
        if ($health['success']) {
            echo "  ✅ System healthy\n";
        } else {
            echo "  ❌ System health issues detected!\n";
        }
        
        // Get recent events
        $recentEvents = $client->getRecentEvents(50);
        if ($recentEvents['success']) {
            $total = $recentEvents['data']['total'];
            echo "  📊 Total events: $total\n";
            
            // Count by status
            $statusCounts = [];
            foreach ($recentEvents['data']['data'] as $event) {
                $status = $event['status'];
                $statusCounts[$status] = ($statusCounts[$status] ?? 0) + 1;
            }
            
            foreach ($statusCounts as $status => $count) {
                $icon = $status === 'sent' ? '✅' : ($status === 'pending' ? '⏳' : '❌');
                echo "    $icon $status: $count\n";
            }
            
            // Calculate rate if we have previous stats
            if ($previousStats) {
                $newEvents = $total - $previousStats['total'];
                $rate = $newEvents / ($interval / 60); // events per minute
                echo "  📈 Event rate: " . round($rate, 2) . " events/minute\n";
            }
            
            $previousStats = ['total' => $total, 'time' => time()];
        }
        
        echo "\n";
        
        // Wait for next check
        if (time() + $interval < $endTime) {
            sleep($interval);
        } else {
            break;
        }
    }
    
    echo "🏁 Monitoring completed.\n";
}

// Start monitoring
monitorSystemRealTime(180); // Monitor for 3 minutes
?>
```

---

## 🚨 Error Handling & Validation

### 1. Comprehensive Error Handler

```php
<?php
/**
 * Enhanced API Client with Error Handling
 */
class CleverTapApiClientWithErrorHandling extends CleverTapApiClient
{
    private $logger;

    public function __construct($baseUrl = 'http://localhost:8000', $timeout = 30)
    {
        parent::__construct($baseUrl, $timeout);
        $this->logger = new ErrorLogger();
    }

    /**
     * Send event with validation and error handling
     */
    public function sendEventSafe($userId, $eventName, $message = null, $customData = [])
    {
        try {
            // Validate input
            $this->validateEventInput($userId, $eventName);

            // Send event
            $result = $this->sendEvent($userId, $eventName, $message, $customData);

            if (!$result['success']) {
                $this->handleApiError($result, 'send_event', compact('userId', 'eventName'));
            }

            return $result;

        } catch (ValidationException $e) {
            $this->logger->logValidationError($e, compact('userId', 'eventName'));
            return ['success' => false, 'error' => 'validation', 'message' => $e->getMessage()];

        } catch (Exception $e) {
            $this->logger->logSystemError($e, compact('userId', 'eventName'));
            return ['success' => false, 'error' => 'system', 'message' => $e->getMessage()];
        }
    }

    /**
     * Validate event input
     */
    private function validateEventInput($userId, $eventName)
    {
        if (empty($userId)) {
            throw new ValidationException('User ID is required');
        }

        if (empty($eventName)) {
            throw new ValidationException('Event name is required');
        }

        if (!preg_match('/^[a-zA-Z0-9_]+$/', $eventName)) {
            throw new ValidationException('Event name can only contain letters, numbers, and underscores');
        }

        if (strlen($eventName) > 100) {
            throw new ValidationException('Event name cannot exceed 100 characters');
        }

        if (strlen($userId) > 255) {
            throw new ValidationException('User ID cannot exceed 255 characters');
        }
    }

    /**
     * Handle API errors
     */
    private function handleApiError($result, $operation, $context)
    {
        $errorData = [
            'operation' => $operation,
            'http_code' => $result['http_code'],
            'response' => $result['data'],
            'context' => $context,
            'timestamp' => date('c')
        ];

        switch ($result['http_code']) {
            case 400:
                $this->logger->logBadRequest($errorData);
                break;
            case 401:
                $this->logger->logUnauthorized($errorData);
                break;
            case 500:
                $this->logger->logServerError($errorData);
                break;
            default:
                $this->logger->logGenericError($errorData);
        }
    }
}

/**
 * Custom Exception Classes
 */
class ValidationException extends Exception {}

/**
 * Error Logger
 */
class ErrorLogger
{
    private $logFile;

    public function __construct($logFile = 'clevertap_errors.log')
    {
        $this->logFile = $logFile;
    }

    public function logValidationError($exception, $context)
    {
        $this->writeLog('VALIDATION_ERROR', $exception->getMessage(), $context);
    }

    public function logSystemError($exception, $context)
    {
        $this->writeLog('SYSTEM_ERROR', $exception->getMessage(), $context);
    }

    public function logBadRequest($errorData)
    {
        $this->writeLog('BAD_REQUEST', 'API returned 400', $errorData);
    }

    public function logUnauthorized($errorData)
    {
        $this->writeLog('UNAUTHORIZED', 'API returned 401', $errorData);
    }

    public function logServerError($errorData)
    {
        $this->writeLog('SERVER_ERROR', 'API returned 500', $errorData);
    }

    public function logGenericError($errorData)
    {
        $this->writeLog('GENERIC_ERROR', 'Unknown API error', $errorData);
    }

    private function writeLog($level, $message, $context)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[$timestamp] [$level] $message " . json_encode($context) . "\n";
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}
?>
```

### 2. Testing Scenarios

```php
<?php
/**
 * Comprehensive Testing Scenarios
 */
function runTestingScenarios()
{
    $client = new CleverTapApiClientWithErrorHandling();
    $testResults = [];

    echo "🧪 Running Comprehensive Testing Scenarios...\n\n";

    // Test 1: Valid Event
    echo "1️⃣ Testing Valid Event...\n";
    $result = $client->sendEventSafe('test_user_001', 'valid_test_event', 'Valid test message');
    $testResults['valid_event'] = $result['success'];
    echo $result['success'] ? "✅ Valid event test passed\n" : "❌ Valid event test failed\n";

    // Test 2: Invalid User ID
    echo "\n2️⃣ Testing Invalid User ID...\n";
    $result = $client->sendEventSafe('', 'test_event', 'Test with empty user ID');
    $testResults['invalid_user_id'] = !$result['success'] && $result['error'] === 'validation';
    echo $testResults['invalid_user_id'] ? "✅ Invalid user ID validation passed\n" : "❌ Invalid user ID validation failed\n";

    // Test 3: Invalid Event Name
    echo "\n3️⃣ Testing Invalid Event Name...\n";
    $result = $client->sendEventSafe('test_user', 'invalid-event-name!@#', 'Test with invalid event name');
    $testResults['invalid_event_name'] = !$result['success'] && $result['error'] === 'validation';
    echo $testResults['invalid_event_name'] ? "✅ Invalid event name validation passed\n" : "❌ Invalid event name validation failed\n";

    // Test 4: Long Event Name
    echo "\n4️⃣ Testing Long Event Name...\n";
    $longEventName = str_repeat('a', 101); // 101 characters
    $result = $client->sendEventSafe('test_user', $longEventName, 'Test with long event name');
    $testResults['long_event_name'] = !$result['success'] && $result['error'] === 'validation';
    echo $testResults['long_event_name'] ? "✅ Long event name validation passed\n" : "❌ Long event name validation failed\n";

    // Test 5: Large Custom Data
    echo "\n5️⃣ Testing Large Custom Data...\n";
    $largeCustomData = [
        'large_field' => str_repeat('x', 10000), // 10KB of data
        'array_data' => array_fill(0, 1000, 'test'),
        'nested' => [
            'level1' => [
                'level2' => [
                    'level3' => 'deep_nested_value'
                ]
            ]
        ]
    ];
    $result = $client->sendEventSafe('test_user', 'large_data_test', 'Test with large custom data', $largeCustomData);
    $testResults['large_custom_data'] = $result['success'];
    echo $result['success'] ? "✅ Large custom data test passed\n" : "❌ Large custom data test failed\n";

    // Test 6: Concurrent Events
    echo "\n6️⃣ Testing Concurrent Events...\n";
    $concurrentResults = [];
    $processes = [];

    for ($i = 0; $i < 5; $i++) {
        $pid = pcntl_fork();
        if ($pid == 0) {
            // Child process
            $result = $client->sendEventSafe("concurrent_user_$i", 'concurrent_test', "Concurrent test $i");
            exit($result['success'] ? 0 : 1);
        } else {
            $processes[] = $pid;
        }
    }

    // Wait for all processes to complete
    $concurrentSuccess = 0;
    foreach ($processes as $pid) {
        $status = pcntl_waitpid($pid, $status);
        if (pcntl_wexitstatus($status) === 0) {
            $concurrentSuccess++;
        }
    }

    $testResults['concurrent_events'] = $concurrentSuccess === 5;
    echo $testResults['concurrent_events'] ? "✅ Concurrent events test passed ($concurrentSuccess/5)\n" : "❌ Concurrent events test failed ($concurrentSuccess/5)\n";

    // Test 7: Network Timeout Simulation
    echo "\n7️⃣ Testing Network Timeout...\n";
    $timeoutClient = new CleverTapApiClientWithErrorHandling('http://httpbin.org/delay/10', 5); // 5 second timeout
    $result = $timeoutClient->sendEventSafe('timeout_user', 'timeout_test', 'Timeout test');
    $testResults['network_timeout'] = !$result['success'] && $result['error'] === 'system';
    echo $testResults['network_timeout'] ? "✅ Network timeout handling passed\n" : "❌ Network timeout handling failed\n";

    // Test Summary
    echo "\n📊 Testing Summary:\n";
    $passedTests = array_sum($testResults);
    $totalTests = count($testResults);
    echo "Passed: $passedTests/$totalTests tests\n";

    foreach ($testResults as $test => $passed) {
        $status = $passed ? '✅' : '❌';
        echo "$status " . ucfirst(str_replace('_', ' ', $test)) . "\n";
    }

    return $passedTests === $totalTests;
}

// Run testing scenarios
if (runTestingScenarios()) {
    echo "\n🎉 All tests passed! System is robust and ready for production.\n";
} else {
    echo "\n⚠️ Some tests failed. Please review the system implementation.\n";
}
?>
```

---

## 📊 Performance Testing

```php
<?php
/**
 * Performance Testing Suite
 */
function runPerformanceTests()
{
    $client = new CleverTapApiClient();

    echo "⚡ Running Performance Tests...\n\n";

    // Test 1: Single Event Performance
    echo "1️⃣ Testing Single Event Performance...\n";
    $iterations = 10;
    $times = [];

    for ($i = 0; $i < $iterations; $i++) {
        $start = microtime(true);
        $result = $client->sendEvent("perf_user_$i", 'performance_test', 'Performance test event');
        $end = microtime(true);

        if ($result['success']) {
            $times[] = ($end - $start) * 1000; // Convert to milliseconds
        }
    }

    if (!empty($times)) {
        $avgTime = array_sum($times) / count($times);
        $minTime = min($times);
        $maxTime = max($times);

        echo "  📈 Average response time: " . round($avgTime, 2) . "ms\n";
        echo "  ⚡ Fastest response: " . round($minTime, 2) . "ms\n";
        echo "  🐌 Slowest response: " . round($maxTime, 2) . "ms\n";
        echo "  ✅ Success rate: " . round((count($times) / $iterations) * 100, 2) . "%\n";
    }

    // Test 2: Bulk Event Performance
    echo "\n2️⃣ Testing Bulk Event Performance...\n";
    $bulkSize = 50;
    $bulkStart = microtime(true);
    $bulkSuccess = 0;

    for ($i = 0; $i < $bulkSize; $i++) {
        $result = $client->sendEvent("bulk_user_$i", 'bulk_performance_test', "Bulk test event $i");
        if ($result['success']) {
            $bulkSuccess++;
        }
    }

    $bulkEnd = microtime(true);
    $bulkTime = ($bulkEnd - $bulkStart) * 1000;
    $throughput = $bulkSuccess / (($bulkEnd - $bulkStart) / 60); // events per minute

    echo "  📦 Processed $bulkSuccess/$bulkSize events\n";
    echo "  ⏱️ Total time: " . round($bulkTime, 2) . "ms\n";
    echo "  🚀 Throughput: " . round($throughput, 2) . " events/minute\n";
    echo "  📊 Average per event: " . round($bulkTime / $bulkSize, 2) . "ms\n";

    // Test 3: Memory Usage
    echo "\n3️⃣ Testing Memory Usage...\n";
    $memoryStart = memory_get_usage(true);
    $peakMemoryStart = memory_get_peak_usage(true);

    // Process events and monitor memory
    for ($i = 0; $i < 100; $i++) {
        $client->sendEvent("memory_user_$i", 'memory_test', 'Memory usage test', [
            'iteration' => $i,
            'large_data' => str_repeat('x', 1000) // 1KB per event
        ]);
    }

    $memoryEnd = memory_get_usage(true);
    $peakMemoryEnd = memory_get_peak_usage(true);

    echo "  💾 Memory used: " . formatBytes($memoryEnd - $memoryStart) . "\n";
    echo "  📈 Peak memory increase: " . formatBytes($peakMemoryEnd - $peakMemoryStart) . "\n";
    echo "  🔄 Memory per event: " . formatBytes(($memoryEnd - $memoryStart) / 100) . "\n";
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}

// Run performance tests
runPerformanceTests();
?>
```

This comprehensive PHP documentation provides:

1. **Complete API Client Class** - Production-ready PHP client
2. **Advanced Usage Examples** - Real-world scenarios
3. **Batch Processing** - Handle multiple events efficiently
4. **Error Handling** - Robust error management
5. **Validation** - Input validation and sanitization
6. **Testing Scenarios** - Comprehensive test suite
7. **Performance Testing** - Benchmarking and optimization
8. **Monitoring Tools** - Real-time system monitoring

All code examples are properly formatted, include error handling, and follow PHP best practices! 🚀
```
