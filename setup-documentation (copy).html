<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel 5.6 + Redis Queue + Supervisor Setup Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }
        
        .section h2 {
            color: #007bff;
            margin-bottom: 20px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
        }
        
        .section h3 {
            color: #495057;
            margin: 20px 0 10px 0;
            font-size: 1.3em;
        }
        
        .step {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .step-number {
            background: #28a745;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .command {
            background: #2d3748;
            color: #68d391;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 8px 0;
            overflow-x: auto;
            border-left: 4px solid #68d391;
            position: relative;
            font-size: 14px;
            line-height: 1.4;
        }

        .command::before {
            content: "$ ";
            color: #9ca3af;
            font-weight: bold;
        }

        .command:hover {
            background: #374151;
            cursor: pointer;
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }

        .copy-btn {
            position: absolute;
            top: 5px;
            right: 10px;
            background: #4a5568;
            color: #e2e8f0;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .command:hover .copy-btn {
            opacity: 1;
        }
        
        .file-content {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            border: 1px solid #4a5568;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #f39c12;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #17a2b8;
        }
        
        .icon {
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .toc {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .toc h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            color: #007bff;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .toc a:hover {
            background: #007bff;
            color: white;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            background: #343a40;
            color: white;
            border-radius: 10px;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .command, .file-content {
                font-size: 12px;
            }
        }
    </style>
    <script>
        function copyCommand(element) {
            const text = element.textContent.replace('$ ', '').trim();
            navigator.clipboard.writeText(text).then(() => {
                const btn = element.querySelector('.copy-btn');
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.background = '#48bb78';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '#4a5568';
                }, 2000);
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Add copy buttons to all command blocks
            const commands = document.querySelectorAll('.command');
            commands.forEach(cmd => {
                const copyBtn = document.createElement('button');
                copyBtn.className = 'copy-btn';
                copyBtn.textContent = 'Copy';
                copyBtn.onclick = (e) => {
                    e.stopPropagation();
                    copyCommand(cmd);
                };
                cmd.appendChild(copyBtn);
                cmd.onclick = () => copyCommand(cmd);
            });
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Laravel 5.6 + Redis Queue + Supervisor</h1>
            <p>Complete Setup Documentation</p>
        </div>

        <div class="toc">
            <h3>📋 Table of Contents</h3>
            <ul>
                <li><a href="#overview">🎯 Project Overview</a></li>
                <li><a href="#prerequisites">📋 Prerequisites</a></li>
                <li><a href="#laravel-setup">🏗️ Laravel 5.6 Setup</a></li>
                <li><a href="#redis-setup">🔴 Redis Installation</a></li>
                <li><a href="#queue-config">⚙️ Queue Configuration</a></li>
                <li><a href="#supervisor-setup">👨‍💼 Supervisor Setup</a></li>
                <li><a href="#testing">🧪 Testing & Verification</a></li>
                <li><a href="#monitoring">📊 Monitoring & Management</a></li>
            </ul>
        </div>

        <div class="section" id="overview">
            <h2><span class="icon">🎯</span>Project Overview</h2>
            <p>This documentation covers the complete setup process for a Laravel 5.6 application with Redis Queue and Supervisor process management. The setup includes:</p>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li>✅ Laravel 5.6 framework installation</li>
                <li>✅ Redis server configuration</li>
                <li>✅ Queue system with Redis backend</li>
                <li>✅ Supervisor for queue worker management</li>
                <li>✅ CleverTap analytics integration</li>
                <li>✅ Production-ready configuration</li>
            </ul>
        </div>

        <div class="section" id="prerequisites">
            <h2><span class="icon">📋</span>Prerequisites</h2>
            
            <div class="step">
                <h3><span class="step-number">1</span>System Requirements</h3>
                <p>Ensure your system meets the following requirements:</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>PHP 7.1.3 or higher (We used PHP 7.4.33)</li>
                    <li>Composer dependency manager</li>
                    <li>Redis server</li>
                    <li>Supervisor process control system</li>
                    <li>MySQL/MariaDB database</li>
                </ul>
            </div>

            <div class="step">
                <h3><span class="step-number">2</span>Check PHP Version</h3>
                <div class="command">php -v</div>
                <div class="success">
                    <strong>✅ Expected Output:</strong><br>
                    PHP 7.4.33 (cli) (built: May 9 2025 06:44:39) ( NTS )
                </div>
            </div>
        </div>

        <div class="section" id="laravel-setup">
            <h2><span class="icon">🏗️</span>Laravel 5.6 Setup</h2>
            
            <div class="step">
                <h3><span class="step-number">1</span>Backup Existing App Folder</h3>
                <p>First, we preserved the existing App folder with your custom code:</p>
                <div class="command">cp -r App App_backup</div>
            </div>

            <div class="step">
                <h3><span class="step-number">2</span>Create Laravel 5.6 Project</h3>
                <div class="command">composer create-project --prefer-dist laravel/laravel=5.6.* temp_laravel</div>
                <div class="info">
                    <strong>📝 Note:</strong> This creates a temporary Laravel project that we'll merge with your existing code.
                </div>
            </div>

            <div class="step">
                <h3><span class="step-number">3</span>Move Laravel Files and Restore App</h3>
                <div class="command">mv temp_laravel/* .</div>
                <div class="command">mv temp_laravel/.* . 2>/dev/null || true</div>
                <div class="command">rm -rf temp_laravel</div>
                <div class="command">rm -rf app</div>
                <div class="command">mv App_backup app</div>
            </div>

            <div class="step">
                <h3><span class="step-number">4</span>Install Redis Package</h3>
                <div class="command">composer require predis/predis</div>
                <div class="warning">
                    <strong>⚠️ Important:</strong> We had to create missing Laravel core files (Console/Kernel.php, Providers, etc.) to resolve autoloading issues.
                </div>
            </div>
        </div>

        <div class="section" id="redis-setup">
            <h2><span class="icon">🔴</span>Redis Installation & Configuration</h2>

            <div class="step">
                <h3><span class="step-number">1</span>Install Redis Server</h3>
                <div class="command">sudo apt update && sudo apt install redis-server -y</div>
            </div>

            <div class="step">
                <h3><span class="step-number">2</span>Start and Enable Redis</h3>
                <div class="command">sudo systemctl start redis-server</div>
                <div class="command">sudo systemctl enable redis-server</div>
            </div>

            <div class="step">
                <h3><span class="step-number">3</span>Test Redis Connection</h3>
                <div class="command">redis-cli ping</div>
                <div class="success">
                    <strong>✅ Expected Output:</strong> PONG
                </div>
            </div>
        </div>

        <div class="section" id="queue-config">
            <h2><span class="icon">⚙️</span>Queue Configuration</h2>

            <div class="step">
                <h3><span class="step-number">1</span>Update .env Configuration</h3>
                <p>Modified the queue driver to use Redis:</p>
                <div class="file-content">
# Queue Configuration
QUEUE_DRIVER=redis

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
                </div>
            </div>

            <div class="step">
                <h3><span class="step-number">2</span>Create Analytics Events Configuration</h3>
                <p>Created <code>config/analytics_events.php</code> for CleverTap event mapping:</p>
                <div class="file-content">
&lt;?php
return [
    'auth/getplayerlist' => [
        'event' => 'player_list_viewed',
        'message' => 'Player List Viewed'
    ],
    'auth/joinleague' => [
        'event' => 'contest_joined',
        'message' => 'Contest Joined'
    ],
    // ... more event mappings
];
                </div>
            </div>

            <div class="step">
                <h3><span class="step-number">3</span>Create Failed Jobs Migration</h3>
                <div class="command">php artisan queue:failed-table</div>
                <div class="info">
                    <strong>📝 Note:</strong> This creates a migration for tracking failed queue jobs.
                </div>
            </div>
        </div>

        <div class="section" id="supervisor-setup">
            <h2><span class="icon">👨‍💼</span>Supervisor Setup</h2>

            <div class="step">
                <h3><span class="step-number">1</span>Install Supervisor</h3>
                <div class="command">sudo apt install supervisor -y</div>
            </div>

            <div class="step">
                <h3><span class="step-number">2</span>Create Supervisor Configuration</h3>
                <p>Created <code>laravel-queue-worker.conf</code>:</p>
                <div class="file-content">
[program:laravel-queue-worker]
process_name=%(program_name)s_%(process_num)02d
command=php "/home/<USER>/Documents/development/my document/Laravel/visoion11/forErroFixing/artisan" queue:work redis --sleep=3 --tries=3 --timeout=60
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=kapil
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/laravel-queue-worker.log
stopwaitsecs=3600
                </div>
            </div>

            <div class="step">
                <h3><span class="step-number">3</span>Deploy Configuration</h3>
                <div class="command">sudo cp laravel-queue-worker.conf /etc/supervisor/conf.d/</div>
                <div class="command">sudo supervisorctl reread</div>
                <div class="command">sudo supervisorctl update</div>
            </div>

            <div class="step">
                <h3><span class="step-number">4</span>Start Queue Workers</h3>
                <div class="command">sudo supervisorctl start laravel-queue-worker:*</div>
            </div>

            <div class="warning">
                <strong>⚠️ Troubleshooting:</strong> We encountered an issue with the <code>--max-time</code> option not existing in Laravel 5.6, so we replaced it with <code>--timeout=60</code>.
            </div>
        </div>

        <div class="section" id="testing">
            <h2><span class="icon">🧪</span>Testing & Verification</h2>

            <div class="step">
                <h3><span class="step-number">1</span>Check Supervisor Status</h3>
                <div class="command">sudo supervisorctl status</div>
                <div class="success">
                    <strong>✅ Expected Output:</strong><br>
                    laravel-queue-worker:laravel-queue-worker_00   RUNNING   pid 1009652, uptime 0:01:30<br>
                    laravel-queue-worker:laravel-queue-worker_01   RUNNING   pid 1009653, uptime 0:01:30
                </div>
            </div>

            <div class="step">
                <h3><span class="step-number">2</span>Test Queue Job</h3>
                <p>Added a test route in <code>routes/web.php</code>:</p>
                <div class="file-content">
Route::get('/test-queue', function () {
    dispatch(new \App\Jobs\ProcessTestJob([
        'message' => 'Queue test from web route',
        'timestamp' => now()
    ]));
    return 'Queue job dispatched! Check logs.';
});
                </div>
            </div>

            <div class="step">
                <h3><span class="step-number">3</span>Manual Queue Test</h3>
                <div class="command">php artisan queue:work redis --once</div>
                <div class="info">
                    <strong>📝 Note:</strong> This processes one job and exits, useful for testing.
                </div>
            </div>
        </div>

        <div class="section" id="monitoring">
            <h2><span class="icon">📊</span>Monitoring & Management</h2>

            <div class="step">
                <h3><span class="step-number">1</span>Supervisor Commands</h3>
                <p><strong>Check status:</strong></p>
                <div class="command">sudo supervisorctl status</div>

                <p><strong>Start workers:</strong></p>
                <div class="command">sudo supervisorctl start laravel-queue-worker:*</div>

                <p><strong>Stop workers:</strong></p>
                <div class="command">sudo supervisorctl stop laravel-queue-worker:*</div>

                <p><strong>Restart workers:</strong></p>
                <div class="command">sudo supervisorctl restart laravel-queue-worker:*</div>

                <p><strong>Reload configuration:</strong></p>
                <div class="command">sudo supervisorctl reread</div>
                <div class="command">sudo supervisorctl update</div>
            </div>

            <div class="step">
                <h3><span class="step-number">2</span>Log Monitoring</h3>
                <p><strong>Watch queue worker logs:</strong></p>
                <div class="command">sudo tail -f /var/log/laravel-queue-worker.log</div>

                <p><strong>Watch Laravel application logs:</strong></p>
                <div class="command">tail -f storage/logs/laravel.log</div>
            </div>

            <div class="step">
                <h3><span class="step-number">3</span>Redis Monitoring</h3>
                <p><strong>Check Redis connection:</strong></p>
                <div class="command">redis-cli ping</div>

                <p><strong>Monitor Redis commands:</strong></p>
                <div class="command">redis-cli monitor</div>

                <p><strong>Check queue length:</strong></p>
                <div class="command">redis-cli llen queues:default</div>
            </div>
        </div>

        <div class="section">
            <h2><span class="icon">🎯</span>Your Analytics Integration</h2>

            <div class="step">
                <h3><span class="step-number">1</span>Middleware Integration</h3>
                <p>Your existing <code>AnalyticsEventLoggerMiddleware</code> now works with Redis Queue:</p>
                <div class="file-content">
// In middleware - dispatches to Redis Queue
dispatch(new LogAnalyticsEventJob($identity, $event, $message, $customData));
                </div>
            </div>

            <div class="step">
                <h3><span class="step-number">2</span>Job Processing Flow</h3>
                <ol style="margin: 10px 0; padding-left: 20px;">
                    <li>🌐 API request hits middleware</li>
                    <li>📊 Analytics data is transformed</li>
                    <li>📤 Job dispatched to Redis queue</li>
                    <li>👨‍💼 Supervisor workers pick up job</li>
                    <li>🚀 CleverTap API call executed</li>
                    <li>📝 Results logged</li>
                </ol>
            </div>

            <div class="step">
                <h3><span class="step-number">3</span>Benefits Achieved</h3>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>✅ Non-blocking API responses</li>
                    <li>✅ Automatic job retry on failure</li>
                    <li>✅ Scalable worker processes</li>
                    <li>✅ Persistent queue storage</li>
                    <li>✅ Process monitoring & management</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <h3>🎉 Setup Complete!</h3>
            <p>Your Laravel 5.6 application with Redis Queue and Supervisor is now fully configured and running.</p>
            <p style="margin-top: 10px; opacity: 0.8;">
                <strong>Next Steps:</strong> Update database credentials, run migrations, and test your CleverTap integration.
            </p>
        </div>
    </div>
</body>
</html>
