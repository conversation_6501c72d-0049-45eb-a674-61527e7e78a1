<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DocumentationController;
use App\Http\Controllers\DocumentationViewController;

/*
|--------------------------------------------------------------------------
| Documentation Routes
|--------------------------------------------------------------------------
|
| Here are the routes for the dynamic documentation system.
| These routes handle both admin CRUD operations and public viewing.
|
*/

// Public Documentation Routes
Route::group(['prefix' => 'documentation'], function () {
    Route::get('/', [DocumentationViewController::class, 'index'])->name('documentation.index');
    Route::get('/search', [DocumentationViewController::class, 'search'])->name('documentation.search');
    Route::get('/export/{format}', [DocumentationViewController::class, 'export'])->name('documentation.export');
    Route::get('/api', [DocumentationViewController::class, 'api'])->name('documentation.api');
    Route::get('/{category}/{section}', [DocumentationViewController::class, 'section'])->name('documentation.section');
});

// Admin Documentation Management Routes
Route::group(['prefix' => 'admin/documentation', 'as' => 'admin.documentation.'], function () {
    
    // Dashboard
    Route::get('/', [DocumentationController::class, 'dashboard'])->name('dashboard');
    Route::get('/preview', [DocumentationController::class, 'preview'])->name('preview');
    Route::get('/generate', [DocumentationController::class, 'generate'])->name('generate');
    
    // Categories Management
    Route::group(['prefix' => 'categories', 'as' => 'categories.'], function () {
        Route::get('/', [DocumentationController::class, 'categories'])->name('index');
        Route::get('/create', [DocumentationController::class, 'createCategory'])->name('create');
        Route::post('/', [DocumentationController::class, 'storeCategory'])->name('store');
        Route::get('/{category}/edit', [DocumentationController::class, 'editCategory'])->name('edit');
        Route::put('/{category}', [DocumentationController::class, 'updateCategory'])->name('update');
        Route::delete('/{category}', [DocumentationController::class, 'deleteCategory'])->name('delete');
    });
    
    // Sections Management
    Route::group(['prefix' => 'sections', 'as' => 'sections.'], function () {
        Route::get('/', [DocumentationController::class, 'sections'])->name('index');
        Route::get('/create', [DocumentationController::class, 'createSection'])->name('create');
        Route::post('/', [DocumentationController::class, 'storeSection'])->name('store');
        Route::get('/{section}/edit', [DocumentationController::class, 'editSection'])->name('edit');
        Route::put('/{section}', [DocumentationController::class, 'updateSection'])->name('update');
        Route::delete('/{section}', [DocumentationController::class, 'deleteSection'])->name('delete');
    });
    
    // Contents Management
    Route::group(['prefix' => 'contents', 'as' => 'contents.'], function () {
        Route::get('/', [DocumentationController::class, 'contents'])->name('index');
        Route::get('/create', [DocumentationController::class, 'createContent'])->name('create');
        Route::post('/', [DocumentationController::class, 'storeContent'])->name('store');
        Route::get('/{content}/edit', [DocumentationController::class, 'editContent'])->name('edit');
        Route::put('/{content}', [DocumentationController::class, 'updateContent'])->name('update');
        Route::delete('/{content}', [DocumentationController::class, 'deleteContent'])->name('delete');
    });
    
    // Bulk Operations
    Route::post('/import', [DocumentationViewController::class, 'bulkImport'])->name('import');
    Route::get('/export-data', function() {
        $categories = \App\Models\DocCategory::with(['sections.contents'])->get();
        return response()->json(['categories' => $categories]);
    })->name('export.data');
});

// API Routes for AJAX operations
Route::group(['prefix' => 'api/documentation', 'as' => 'api.documentation.'], function () {
    
    // Get sections by category
    Route::get('/categories/{category}/sections', function(\App\Models\DocCategory $category) {
        return response()->json($category->activeSections);
    })->name('category.sections');
    
    // Get contents by section
    Route::get('/sections/{section}/contents', function(\App\Models\DocSection $section) {
        return response()->json($section->activeContents);
    })->name('section.contents');
    
    // Search content
    Route::get('/search', function() {
        $query = request('q');
        $contents = \App\Models\DocContent::active()
                                         ->with(['section.category'])
                                         ->where(function($q) use ($query) {
                                             $q->where('title', 'LIKE', "%{$query}%")
                                               ->orWhere('content', 'LIKE', "%{$query}%");
                                         })
                                         ->limit(20)
                                         ->get();
        return response()->json($contents);
    })->name('search');
    
    // Update sort order
    Route::post('/sort', function() {
        $items = request('items');
        $type = request('type'); // categories, sections, contents
        
        foreach ($items as $item) {
            $model = null;
            switch ($type) {
                case 'categories':
                    $model = \App\Models\DocCategory::find($item['id']);
                    break;
                case 'sections':
                    $model = \App\Models\DocSection::find($item['id']);
                    break;
                case 'contents':
                    $model = \App\Models\DocContent::find($item['id']);
                    break;
            }
            
            if ($model) {
                $model->update(['sort_order' => $item['sort_order']]);
            }
        }
        
        return response()->json(['success' => true]);
    })->name('sort');
    
    // Toggle active status
    Route::post('/{type}/{id}/toggle', function($type, $id) {
        $model = null;
        switch ($type) {
            case 'categories':
                $model = \App\Models\DocCategory::find($id);
                break;
            case 'sections':
                $model = \App\Models\DocSection::find($id);
                break;
            case 'contents':
                $model = \App\Models\DocContent::find($id);
                break;
        }
        
        if ($model) {
            $model->update(['is_active' => !$model->is_active]);
            return response()->json(['success' => true, 'is_active' => $model->is_active]);
        }
        
        return response()->json(['success' => false], 404);
    })->name('toggle');
    
    // Duplicate item
    Route::post('/{type}/{id}/duplicate', function($type, $id) {
        $original = null;
        switch ($type) {
            case 'categories':
                $original = \App\Models\DocCategory::find($id);
                break;
            case 'sections':
                $original = \App\Models\DocSection::find($id);
                break;
            case 'contents':
                $original = \App\Models\DocContent::find($id);
                break;
        }
        
        if ($original) {
            $duplicate = $original->replicate();
            $duplicate->name = $duplicate->name . ' (Copy)';
            $duplicate->title = $duplicate->title . ' (Copy)';
            $duplicate->slug = $duplicate->slug . '-copy';
            $duplicate->save();
            
            return response()->json(['success' => true, 'item' => $duplicate]);
        }
        
        return response()->json(['success' => false], 404);
    })->name('duplicate');
});

// Include these routes in your main web.php file:
// require __DIR__.'/documentation.php';
