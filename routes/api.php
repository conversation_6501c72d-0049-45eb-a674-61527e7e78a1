<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

// Simple test route with CleverTap middleware
Route::get('/test-clevertap', function () {
    return response()->json([
        'success' => true,
        'message' => 'CleverTap middleware test successful',
        'timestamp' => now()->toISOString()
    ]);
})->middleware('clevertap');

// User Details API with CleverTap Logging Middleware
Route::group(['prefix' => 'users', 'middleware' => ['clevertap']], function () {

    // Get single user details
    Route::post('/details', 'Api\UserController@getUserDetails');

    // Get multiple users details
    Route::post('/multiple', 'Api\UserController@getMultipleUsers');

    // Get user profile with extended details
    Route::post('/profile', 'Api\UserController@getUserProfile');

    // Search users
    Route::post('/search', 'Api\UserController@searchUsers');

});

// Additional API endpoints with CleverTap logging
Route::group(['middleware' => ['clevertap']], function () {

    // Team player endpoints
    Route::post('/team/players', function(Request $request) {
        $request->validate([
            'team_id' => 'required|string',
            'user_id' => 'required|string'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Team players retrieved successfully',
            'data' => [
                'team_id' => $request->team_id,
                'players' => [
                    ['id' => '1', 'name' => 'Player 1', 'role' => 'Captain'],
                    ['id' => '2', 'name' => 'Player 2', 'role' => 'Player'],
                    ['id' => '3', 'name' => 'Player 3', 'role' => 'Player']
                ]
            ]
        ]);
    });

    // Player stats endpoint
    Route::post('/player/stats', function(Request $request) {
        $request->validate([
            'player_id' => 'required|string',
            'user_id' => 'required|string'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Player stats retrieved successfully',
            'data' => [
                'player_id' => $request->player_id,
                'stats' => [
                    'games_played' => rand(10, 100),
                    'wins' => rand(5, 50),
                    'losses' => rand(5, 50),
                    'score' => rand(1000, 5000)
                ]
            ]
        ]);
    });

    // Game history endpoint
    Route::post('/game/history', function(Request $request) {
        $request->validate([
            'user_id' => 'required|string',
            'limit' => 'integer|min:1|max:50'
        ]);

        $limit = $request->input('limit', 10);
        $games = [];

        for ($i = 1; $i <= $limit; $i++) {
            $games[] = [
                'game_id' => "game_{$i}",
                'date' => now()->subDays(rand(1, 30))->toDateString(),
                'result' => ['win', 'loss', 'draw'][rand(0, 2)],
                'score' => rand(100, 1000)
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Game history retrieved successfully',
            'data' => $games
        ]);
    });

});

// CleverTap Testing Routes
Route::prefix('clevertap')->group(function () {
    Route::post('/test-queue', 'Api\CleverTapTestController@testCleverTapQueue');
    Route::get('/events-history', 'Api\CleverTapTestController@getEventsHistory');
    Route::post('/update-settings', 'Api\CleverTapTestController@updateSettings');
});

// Analytics Event Route (for your existing middleware)
Route::post('/auth/getplayerlist', function (Request $request) {
    // Simulate your existing API response
    return response()->json([
        'success' => true,
        'message' => 'Player list retrieved successfully',
        'data' => [
            'players' => [
                ['id' => 1, 'name' => 'Player 1', 'score' => 100],
                ['id' => 2, 'name' => 'Player 2', 'score' => 95],
                ['id' => 3, 'name' => 'Player 3', 'score' => 90],
            ],
            'total' => 3
        ]
    ]);
})->middleware('App\Http\Middleware\AnalyticsEventLoggerMiddleware');
