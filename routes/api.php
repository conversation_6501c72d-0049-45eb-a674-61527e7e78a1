<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

// CleverTap Testing Routes
Route::prefix('clevertap')->group(function () {
    Route::post('/test-queue', 'Api\CleverTapTestController@testCleverTapQueue');
    Route::get('/events-history', 'Api\CleverTapTestController@getEventsHistory');
    Route::post('/update-settings', 'Api\CleverTapTestController@updateSettings');
});

// Analytics Event Route (for your existing middleware)
Route::post('/auth/getplayerlist', function (Request $request) {
    // Simulate your existing API response
    return response()->json([
        'success' => true,
        'message' => 'Player list retrieved successfully',
        'data' => [
            'players' => [
                ['id' => 1, 'name' => 'Player 1', 'score' => 100],
                ['id' => 2, 'name' => 'Player 2', 'score' => 95],
                ['id' => 3, 'name' => 'Player 3', 'score' => 90],
            ],
            'total' => 3
        ]
    ]);
})->middleware('App\Http\Middleware\AnalyticsEventLoggerMiddleware');
