<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/test-queue', function () {
    // Test Redis Queue
    dispatch(new \App\Jobs\ProcessTestJob(['message' => 'Queue test from web route', 'timestamp' => now()]));
    return 'Queue job dispatched! Check logs.';
});
