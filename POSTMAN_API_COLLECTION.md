# 📮 Postman API Collection - CleverTap Integration

## 🚀 Complete API Documentation with Request/Response Examples

### Base URL Configuration
```
{{base_url}} = http://localhost:8000
```

---

## 📋 API Endpoints Collection

### 1. 🧪 Test CleverTap Queue Integration

**Endpoint:** `POST {{base_url}}/api/clevertap/test-queue`

**Headers:**
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

**Request Body:**
```json
{
  "user_id": "12501981",
  "event": "test_api_event",
  "message": "Testing CleverTap integration from Postman",
  "custom_data": {
    "source": "postman",
    "test_type": "integration",
    "timestamp": "2025-06-29T19:30:00Z"
  }
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "CleverTap event dispatched to queue successfully",
  "data": {
    "event_id": 8,
    "user_identity": "12501981",
    "event_name": "test_api_event",
    "custom_data": {
      "platform": "PostmanRuntime/7.44.1",
      "ip": "127.0.0.1",
      "test_data": "Sample test data",
      "timestamp": "2025-06-29T19:30:00+00:00",
      "api_endpoint": "test-clevertap-queue",
      "source": "postman",
      "test_type": "integration"
    },
    "queue_status": "dispatched"
  }
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "user_id": ["The user_id field is required."],
    "event": ["The event field is required."]
  }
}
```

**PHP Code Example:**
```php
<?php
// Test CleverTap Queue Integration
$curl = curl_init();

curl_setopt_array($curl, array(
    CURLOPT_URL => 'http://localhost:8000/api/clevertap/test-queue',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => json_encode([
        'user_id' => '12501981',
        'event' => 'test_api_event',
        'message' => 'Testing CleverTap integration',
        'custom_data' => [
            'source' => 'php_script',
            'test_type' => 'integration'
        ]
    ]),
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json',
        'Accept: application/json'
    ),
));

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
curl_close($curl);

$data = json_decode($response, true);

if ($httpCode === 200 && $data['success']) {
    echo "✅ Event dispatched successfully!\n";
    echo "Event ID: " . $data['data']['event_id'] . "\n";
    echo "Status: " . $data['data']['queue_status'] . "\n";
} else {
    echo "❌ Error: " . $data['message'] . "\n";
}
?>
```

---

### 2. 📊 Get CleverTap Events History

**Endpoint:** `GET {{base_url}}/api/clevertap/events-history`

**Headers:**
```json
{
  "Accept": "application/json"
}
```

**Query Parameters:**
```
limit: 10 (optional, default: 50)
offset: 0 (optional, default: 0)
status: sent|pending|failed (optional)
user_id: 12501981 (optional)
```

**Request URL Examples:**
```
GET /api/clevertap/events-history
GET /api/clevertap/events-history?limit=5
GET /api/clevertap/events-history?status=sent&limit=10
GET /api/clevertap/events-history?user_id=12501981&limit=20
```

**Success Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 8,
      "user_identity": "12501981",
      "event_name": "test_api_event",
      "event_data": "{\"platform\":\"PostmanRuntime/7.44.1\",\"ip\":\"127.0.0.1\",\"source\":\"postman\"}",
      "message": "Testing CleverTap integration from Postman",
      "status": "sent",
      "response_data": "{\"success\":true,\"status\":200,\"response\":{\"status\":\"success\",\"processed\":1}}",
      "job_id": "dispatched_1751224200",
      "sent_at": "2025-06-29 19:30:05",
      "created_at": "2025-06-29 19:30:00",
      "updated_at": "2025-06-29 19:30:05"
    },
    {
      "id": 7,
      "user_identity": "12501981",
      "event_name": "player_list_viewed",
      "event_data": "[]",
      "message": "Player List Viewed",
      "status": "sent",
      "response_data": "{\"success\":true,\"status\":200}",
      "job_id": "dispatched_1751223987",
      "sent_at": "2025-06-29 19:06:30",
      "created_at": "2025-06-29 19:06:27",
      "updated_at": "2025-06-29 19:06:30"
    }
  ],
  "total": 8,
  "pagination": {
    "current_page": 1,
    "per_page": 10,
    "total_pages": 1,
    "has_more": false
  }
}
```

**PHP Code Example:**
```php
<?php
// Get CleverTap Events History
function getCleverTapEvents($limit = 10, $status = null, $userId = null) {
    $url = 'http://localhost:8000/api/clevertap/events-history';
    $params = ['limit' => $limit];
    
    if ($status) $params['status'] = $status;
    if ($userId) $params['user_id'] = $userId;
    
    $url .= '?' . http_build_query($params);
    
    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => array('Accept: application/json'),
    ));
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    $data = json_decode($response, true);
    
    if ($httpCode === 200 && $data['success']) {
        echo "📊 Found {$data['total']} events:\n";
        foreach ($data['data'] as $event) {
            echo "- ID: {$event['id']}, Event: {$event['event_name']}, Status: {$event['status']}\n";
        }
        return $data['data'];
    } else {
        echo "❌ Error fetching events\n";
        return false;
    }
}

// Usage examples
getCleverTapEvents(5);                    // Get last 5 events
getCleverTapEvents(10, 'sent');          // Get last 10 sent events
getCleverTapEvents(20, null, '12501981'); // Get last 20 events for user
?>
```

---

### 3. ⚙️ Update CleverTap Settings

**Endpoint:** `POST {{base_url}}/api/clevertap/update-settings`

**Headers:**
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

**Request Body:**
```json
{
  "project_id": "TEST-549-894-847Z",
  "project_token": "TEST-4cb-c45",
  "passcode": "YAS-KUA-CAEL",
  "region": "in1"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "CleverTap settings updated successfully",
  "data": {
    "project_id": "TEST-549-894-847Z",
    "project_token": "TEST-4cb-c45",
    "passcode": "YAS-KUA-CAEL",
    "region": "in1",
    "updated_at": "2025-06-29T19:35:00Z"
  }
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "project_id": ["The project_id field is required."],
    "passcode": ["The passcode field is required."]
  }
}
```

**PHP Code Example:**
```php
<?php
// Update CleverTap Settings
function updateCleverTapSettings($projectId, $projectToken, $passcode, $region = 'in1') {
    $curl = curl_init();
    
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://localhost:8000/api/clevertap/update-settings',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode([
            'project_id' => $projectId,
            'project_token' => $projectToken,
            'passcode' => $passcode,
            'region' => $region
        ]),
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Accept: application/json'
        ),
    ));
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    $data = json_decode($response, true);
    
    if ($httpCode === 200 && $data['success']) {
        echo "✅ CleverTap settings updated successfully!\n";
        echo "Project ID: " . $data['data']['project_id'] . "\n";
        echo "Region: " . $data['data']['region'] . "\n";
        return true;
    } else {
        echo "❌ Error updating settings: " . $data['message'] . "\n";
        if (isset($data['errors'])) {
            foreach ($data['errors'] as $field => $errors) {
                echo "  - $field: " . implode(', ', $errors) . "\n";
            }
        }
        return false;
    }
}

// Usage
updateCleverTapSettings(
    'TEST-549-894-847Z',
    'TEST-4cb-c45', 
    'YAS-KUA-CAEL',
    'in1'
);
?>
```

---

### 4. 🎮 Test Existing Middleware (Player List)

**Endpoint:** `POST {{base_url}}/api/auth/getplayerlist`

**Headers:**
```json
{
  "Content-Type": "application/json",
  "Accept": "application/json"
}
```

**Request Body:**
```json
{
  "user_id": "12501981",
  "matchkey": "89823",
  "sport_key": "CRICKET",
  "challenge_id": "0"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Player list retrieved successfully",
  "data": {
    "players": [
      {
        "id": 1,
        "name": "Player 1",
        "score": 100
      },
      {
        "id": 2,
        "name": "Player 2",
        "score": 95
      },
      {
        "id": 3,
        "name": "Player 3",
        "score": 90
      }
    ],
    "total": 3
  }
}
```

**Note:** This endpoint automatically triggers a CleverTap event `player_list_viewed` via middleware.

**PHP Code Example:**
```php
<?php
// Test Middleware Integration
function getPlayerList($userId, $matchKey, $sportKey, $challengeId = "0") {
    $curl = curl_init();
    
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://localhost:8000/api/auth/getplayerlist',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode([
            'user_id' => $userId,
            'matchkey' => $matchKey,
            'sport_key' => $sportKey,
            'challenge_id' => $challengeId
        ]),
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Accept: application/json'
        ),
    ));
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    $data = json_decode($response, true);
    
    if ($httpCode === 200 && $data['success']) {
        echo "🎮 Player list retrieved successfully!\n";
        echo "Total players: " . $data['data']['total'] . "\n";
        echo "📊 CleverTap event 'player_list_viewed' automatically triggered\n";
        
        // Wait a moment then check if event was created
        sleep(2);
        echo "Checking CleverTap event...\n";
        
        // Check latest events to verify middleware worked
        $eventsResponse = file_get_contents(
            'http://localhost:8000/api/clevertap/events-history?limit=1&user_id=' . $userId
        );
        $eventsData = json_decode($eventsResponse, true);
        
        if ($eventsData['success'] && !empty($eventsData['data'])) {
            $latestEvent = $eventsData['data'][0];
            if ($latestEvent['event_name'] === 'player_list_viewed') {
                echo "✅ CleverTap event created: ID " . $latestEvent['id'] . "\n";
                echo "Status: " . $latestEvent['status'] . "\n";
            }
        }
        
        return $data['data'];
    } else {
        echo "❌ Error getting player list\n";
        return false;
    }
}

// Usage
getPlayerList('12501981', '89823', 'CRICKET', '0');
?>
```

---

### 5. 🔍 Health Check Endpoint

**Endpoint:** `GET {{base_url}}/api/health/clevertap`

**Headers:**
```json
{
  "Accept": "application/json"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "status": "healthy",
  "checks": {
    "redis": {
      "status": "ok",
      "response_time": "2ms"
    },
    "database": {
      "status": "ok",
      "response_time": "5ms"
    },
    "queue_workers": {
      "status": "ok",
      "active_workers": 2,
      "total_workers": 2
    },
    "clevertap_settings": {
      "status": "ok",
      "configured": true
    },
    "recent_events": {
      "last_hour": 15,
      "last_24_hours": 127
    }
  },
  "timestamp": "2025-06-29T19:40:00Z"
}
```

**PHP Code Example:**
```php
<?php
// Health Check
function checkSystemHealth() {
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://localhost:8000/api/health/clevertap',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => array('Accept: application/json'),
    ));

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    $data = json_decode($response, true);

    if ($httpCode === 200 && $data['success']) {
        echo "🏥 System Health Check:\n";
        echo "Overall Status: " . $data['status'] . "\n";

        foreach ($data['checks'] as $service => $check) {
            $status = $check['status'] === 'ok' ? '✅' : '❌';
            echo "$status $service: " . $check['status'] . "\n";
        }

        return $data['status'] === 'healthy';
    } else {
        echo "❌ Health check failed\n";
        return false;
    }
}

// Usage
if (checkSystemHealth()) {
    echo "System is healthy and ready!\n";
} else {
    echo "System has issues that need attention.\n";
}
?>
```

---

## 📦 Postman Collection JSON

### Import this JSON into Postman:

```json
{
  "info": {
    "name": "CleverTap Laravel Integration",
    "description": "Complete API collection for Laravel 5.6 + Redis Queue + CleverTap integration",
    "version": "1.0.0",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:8000",
      "type": "string"
    }
  ],
  "item": [
    {
      "name": "CleverTap Integration",
      "item": [
        {
          "name": "Test Queue Integration",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"user_id\": \"12501981\",\n  \"event\": \"test_api_event\",\n  \"message\": \"Testing CleverTap integration from Postman\",\n  \"custom_data\": {\n    \"source\": \"postman\",\n    \"test_type\": \"integration\",\n    \"timestamp\": \"{{$isoTimestamp}}\"\n  }\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/clevertap/test-queue",
              "host": ["{{base_url}}"],
              "path": ["api", "clevertap", "test-queue"]
            }
          }
        },
        {
          "name": "Get Events History",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{base_url}}/api/clevertap/events-history?limit=10",
              "host": ["{{base_url}}"],
              "path": ["api", "clevertap", "events-history"],
              "query": [
                {
                  "key": "limit",
                  "value": "10"
                },
                {
                  "key": "status",
                  "value": "sent",
                  "disabled": true
                },
                {
                  "key": "user_id",
                  "value": "12501981",
                  "disabled": true
                }
              ]
            }
          }
        },
        {
          "name": "Update CleverTap Settings",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"project_id\": \"TEST-549-894-847Z\",\n  \"project_token\": \"TEST-4cb-c45\",\n  \"passcode\": \"YAS-KUA-CAEL\",\n  \"region\": \"in1\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/clevertap/update-settings",
              "host": ["{{base_url}}"],
              "path": ["api", "clevertap", "update-settings"]
            }
          }
        }
      ]
    },
    {
      "name": "Middleware Testing",
      "item": [
        {
          "name": "Get Player List (Auto CleverTap)",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              },
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"user_id\": \"12501981\",\n  \"matchkey\": \"89823\",\n  \"sport_key\": \"CRICKET\",\n  \"challenge_id\": \"0\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/auth/getplayerlist",
              "host": ["{{base_url}}"],
              "path": ["api", "auth", "getplayerlist"]
            }
          }
        }
      ]
    },
    {
      "name": "System Health",
      "item": [
        {
          "name": "Health Check",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Accept",
                "value": "application/json"
              }
            ],
            "url": {
              "raw": "{{base_url}}/api/health/clevertap",
              "host": ["{{base_url}}"],
              "path": ["api", "health", "clevertap"]
            }
          }
        }
      ]
    }
  ]
}
```
