# 🚀 Quick Setup Guide - Laravel 5.6 + Redis Queue + CleverTap

## ⚡ Quick Start (5 Minutes)

### 1. Environment Setup
```bash
# Copy and update .env file
cp .env.example .env

# Update these values in .env:
DB_DATABASE=redisQueueConnectionDatabase
DB_USERNAME=root
DB_PASSWORD=rgdatabase@2024
QUEUE_CONNECTION=redis
```

### 2. Database Setup
```bash
# Run migrations
php artisan migrate

# Insert CleverTap settings
php -r "
\$pdo = new PDO('mysql:host=127.0.0.1;dbname=redisQueueConnectionDatabase', 'root', 'rgdatabase@2024');
\$settings = json_encode([
    'project_id' => 'TEST-549-894-847Z',
    'project_token' => 'TEST-4cb-c45',
    'passcode' => 'YAS-KUA-CAEL',
    'region' => 'in1'
]);
\$stmt = \$pdo->prepare('INSERT INTO settings (setting_key, value, created_at, updated_at) VALUES (?, ?, NOW(), NOW()) ON DUPLICATE KEY UPDATE value = VALUES(value)');
\$stmt->execute(['CLEVERTAP_DEV_KEY', \$settings]);
echo 'CleverTap settings inserted successfully!';
"
```

### 3. Supervisor Setup
```bash
# Create supervisor config
sudo tee /etc/supervisor/conf.d/laravel-queue-worker.conf > /dev/null <<EOF
[program:laravel-queue-worker]
process_name=%(program_name)s_%(process_num)02d
command=php $(pwd)/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
directory=$(pwd)
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=$(whoami)
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/laravel-queue-worker.log
stopwaitsecs=3600
EOF

# Start supervisor workers
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start laravel-queue-worker:*
```

### 4. Start Application
```bash
# Start Laravel server
php artisan serve --host=0.0.0.0 --port=8000
```

## 🧪 Quick Test

### Test 1: Basic Queue Test
```bash
curl -X POST "http://localhost:8000/api/clevertap/test-queue" \
  -H "Content-Type: application/json" \
  -d '{"user_id":"12501981","event":"quick_test","message":"Quick setup test"}'
```

### Test 2: Middleware Test
```bash
curl -X POST "http://localhost:8000/api/auth/getplayerlist" \
  -H "Content-Type: application/json" \
  -d '{"user_id":"12501981","matchkey":"89823","sport_key":"CRICKET"}'
```

### Test 3: Check Results
```bash
curl "http://localhost:8000/api/clevertap/events-history?limit=3"
```

## ✅ Success Indicators

You should see:
- ✅ HTTP 200 responses from all test endpoints
- ✅ Events with `"status":"sent"` in the history
- ✅ Supervisor workers running: `sudo supervisorctl status`
- ✅ Queue processing: `redis-cli LLEN queues:default` should be 0

## 🔧 Quick Commands

```bash
# Check supervisor status
sudo supervisorctl status

# Monitor queue logs
tail -f /var/log/laravel-queue-worker.log

# Check Laravel logs
tail -f storage/logs/laravel.log

# Restart workers
sudo supervisorctl restart laravel-queue-worker:*

# Clear caches
php artisan config:clear && php artisan cache:clear
```

## 🚨 Troubleshooting

### Issue: Queue jobs not processing
```bash
sudo supervisorctl restart laravel-queue-worker:*
php artisan config:clear
```

### Issue: Database connection error
```bash
# Check database credentials in .env
# Test connection:
php -r "new PDO('mysql:host=127.0.0.1;dbname=redisQueueConnectionDatabase', 'root', 'rgdatabase@2024'); echo 'Connected!';"
```

### Issue: Redis connection error
```bash
# Test Redis:
redis-cli ping
# Should return: PONG
```

## 📊 Monitor Your Setup

### Real-time Monitoring
```bash
# Terminal 1: Monitor queue
watch -n 1 'redis-cli LLEN queues:default'

# Terminal 2: Monitor logs
tail -f storage/logs/laravel.log

# Terminal 3: Monitor workers
watch -n 1 'sudo supervisorctl status'
```

### Health Check
```bash
curl "http://localhost:8000/api/clevertap/events-history" | jq '.total'
```

---

## 🎯 What You Get

- ✅ **Automatic Event Tracking**: API calls automatically trigger CleverTap events
- ✅ **Asynchronous Processing**: Queue jobs handle CleverTap API calls
- ✅ **Reliable Workers**: Supervisor manages 2 queue workers
- ✅ **Complete Logging**: Full audit trail of all events
- ✅ **Error Handling**: Automatic retries and failure tracking
- ✅ **Easy Monitoring**: API endpoints to check status and history

**Your Laravel 5.6 + Redis Queue + CleverTap integration is ready!** 🚀

For detailed documentation, see: `CLEVERTAP_INTEGRATION_DOCUMENTATION.md`
